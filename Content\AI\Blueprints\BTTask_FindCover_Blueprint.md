# BTTask_FindCover - Complete Blueprint Implementation

## 🎯 Blueprint Class Setup

### Class Configuration
- **Parent Class**: `BTTaskNode` (or `BTTask_BlueprintBase`)
- **Blueprint Name**: `BTTask_FindCover_BP`
- **Location**: `/Content/AI/BehaviorTree/Tasks/`
- **Category**: "SquadMate AI Tasks"

## 📋 Blueprint Variables

### 🔑 Blackboard Key Selectors
```cpp
// Input Pins (Category: Blackboard Keys)
TargetActor (Blackboard Key Selector)
├── Key Type: Object (Actor)
├── Default: "TargetActor"
└── Tooltip: "Enemy to take cover from"

CoverLocation (Blackboard Key Selector)
├── Key Type: Vector
├── Default: "CoverLocation"
└── Tooltip: "Selected cover position"

IsUnderFire (Blackboard Key Selector)
├── Key Type: Bool
├── Default: "IsUnderFire"
└── Tooltip: "Whether currently under attack"

SquadRole (Blackboard Key Selector)
├── Key Type: Enum
├── Default: "SquadRole"
└── Tooltip: "Agent's tactical role"
```

### 🏠 Cover Search Settings
```cpp
// Cover Search (Category: Cover Search)
CoverSearchRadius (Float) = 800.0
├── Meta: (ClampMin="200.0", ClampMax="2000.0")
└── Tooltip: "Radius to search for cover"

MinCoverHeight (Float) = 120.0
├── Meta: (ClampMin="50.0", ClampMax="300.0")
└── Tooltip: "Minimum cover height required"

MaxCoverDistance (Float) = 1200.0
├── Meta: (ClampMin="300.0", ClampMax="3000.0")
└── Tooltip: "Maximum distance to cover"

MinDistanceFromEnemy (Float) = 200.0
├── Meta: (ClampMin="50.0", ClampMax="1000.0")
└── Tooltip: "Minimum safe distance from enemies"

PreferOffensiveCover (Boolean) = true
└── Tooltip: "Prefer cover with line of sight to enemy"

RequireFullCover (Boolean) = false
└── Tooltip: "Require complete protection"
```

### 🎯 Cover Scoring Weights
```cpp
// Scoring (Category: Cover Scoring)
DistanceWeight (Float) = 0.3
├── Meta: (ClampMin="0.0", ClampMax="1.0")
└── Tooltip: "Weight for distance scoring"

CoverQualityWeight (Float) = 0.4
├── Meta: (ClampMin="0.0", ClampMax="1.0")
└── Tooltip: "Weight for cover effectiveness"

LineOfSightWeight (Float) = 0.2
├── Meta: (ClampMin="0.0", ClampMax="1.0")
└── Tooltip: "Weight for tactical visibility"

SafetyWeight (Float) = 0.3
├── Meta: (ClampMin="0.0", ClampMax="1.0")
└── Tooltip: "Weight for safety from enemies"

AllyProximityWeight (Float) = 0.15
├── Meta: (ClampMin="0.0", ClampMax="1.0")
└── Tooltip: "Weight for squad coordination"
```

### 🚀 Movement Settings
```cpp
// Movement (Category: Movement)
MoveToCover (Boolean) = true
└── Tooltip: "Automatically move to found cover"

MovementSpeed (Float) = 600.0
├── Meta: (ClampMin="100.0", ClampMax="1200.0")
└── Tooltip: "Speed when moving to cover"

UseCrouchedMovement (Boolean) = true
└── Tooltip: "Move in crouched stance"

UseEvasiveMovement (Boolean) = true
└── Tooltip: "Use zigzag movement pattern"
```

### 🔍 EQS Configuration
```cpp
// EQS (Category: Environmental Query)
CoverQuery (Environmental Query)
└── Tooltip: "EQS query for finding cover"

EmergencyCoverQuery (Environmental Query)
└── Tooltip: "Fast query for emergency cover"

QueryTimeLimit (Float) = 1.5
├── Meta: (ClampMin="0.5", ClampMax="5.0")
└── Tooltip: "Maximum time for EQS query"

MaxQueryResults (Integer) = 10
├── Meta: (ClampMin="3", ClampMax="20")
└── Tooltip: "Maximum cover positions to evaluate"
```

## 🔧 Event Graph Implementation

### 📍 Event Receive Execute AI
```cpp
Event Receive Execute AI
├── Sequence: Input Validation
│   ├── Get Blackboard Component
│   ├── Get Controlled Pawn
│   ├── Branch: Is Valid?
│   │   ├── False → Finish Execute (Failed)
│   │   └── True → Continue
│   └── Function Call: Validate Cover Search Conditions
├── Branch: Should Search for Cover?
│   ├── False → Finish Execute (Failed)
│   └── True → Continue
├── Custom Event: Initialize Cover Search
└── Return Value: In Progress
```

### 🔍 Initialize Cover Search
```cpp
Custom Event: Initialize Cover Search
├── Sequence: Setup Search Parameters
│   ├── Function Call: Get Current Threat Level
│   ├── Function Call: Determine Cover Priority
│   ├── Function Call: Select Appropriate EQS Query
│   │   ├── Branch: Is Emergency?
│   │   │   ├── True → Use EmergencyCoverQuery
│   │   │   └── False → Use CoverQuery
│   │   └── Store Selected Query
│   └── Function Call: Configure Query Parameters
├── Sequence: Start EQS Query
│   ├── Function Call: Run EQS Query
│   │   ├── Query Template: Selected Query
│   │   ├── Querier: Controlled Pawn
│   │   ├── Query Params: Configured Parameters
│   │   └── Finish Delegate: OnCoverQueryComplete
│   ├── Set Timer by Function Name
│   │   ├── Function Name: "OnQueryTimeout"
│   │   ├── Time: QueryTimeLimit
│   │   └── Looping: False
│   └── Set Variable: QueryInProgress (True)
├── Function Call: Broadcast Cover Search Start
└── Function Call: Start Search Animation
```

### ✅ On Cover Query Complete
```cpp
Custom Event: OnCoverQueryComplete
├── Input: Query Result (EQS Query Result)
├── Sequence: Process Results
│   ├── Clear Timer by Function Name ("OnQueryTimeout")
│   ├── Set Variable: QueryInProgress (False)
│   ├── Branch: Has Valid Results?
│   │   ├── False → Custom Event: Handle No Cover Found
│   │   └── True → Continue
│   └── Function Call: Extract Cover Locations
│       ├── Get All Result Locations
│       ├── Filter Invalid Positions
│       └── Store in Array: PotentialCoverSpots
├── Sequence: Evaluate Cover Options
│   ├── For Each Loop: PotentialCoverSpots
│   │   ├── Function Call: Score Cover Position
│   │   ├── Function Call: Validate Cover Accessibility
│   │   ├── Branch: Is Valid Cover?
│   │   │   ├── True → Add to ValidCoverSpots
│   │   │   └── False → Skip
│   │   └── Continue Loop
│   ├── Function Call: Sort Covers by Score
│   └── Function Call: Select Best Cover
├── Sequence: Execute Cover Movement
│   ├── Set Blackboard Value as Vector (CoverLocation)
│   ├── Branch: Move to Cover Enabled?
│   │   ├── True → Custom Event: Move to Selected Cover
│   │   └── False → Custom Event: Complete Cover Search
│   └── Function Call: Broadcast Cover Selected
└── Function Call: Log Cover Decision
```

### 🏃 Move to Selected Cover
```cpp
Custom Event: Move to Selected Cover
├── Input: Cover Location (Vector)
├── Sequence: Setup Movement
│   ├── Function Call: Calculate Movement Path
│   ├── Function Call: Set Movement Speed
│   │   └── Set Max Walk Speed: MovementSpeed
│   ├── Function Call: Configure Movement Stance
│   │   ├── Branch: Use Crouched Movement?
│   │   │   ├── True → Set Stance: Crouching
│   │   │   └── False → Set Stance: Standing
│   │   └── Update Animation State
│   └── Function Call: Setup Evasive Movement
├── Sequence: Execute Movement
│   ├── AI Move To
│   │   ├── Destination: Cover Location
│   │   ├── Acceptance Radius: 100.0
│   │   ├── Stop on Overlap: True
│   │   ├── Use Pathfinding: True
│   │   └── Allow Strafe: UseEvasiveMovement
│   ├── Bind Event: On Move Completed
│   │   └── Custom Event: On Reach Cover
│   └── Set Timer by Function Name
│       ├── Function Name: "Monitor Movement Progress"
│       ├── Time: 0.2
│       ├── Looping: True
│       └── Store Handle: MovementMonitorTimer
├── Function Call: Play Movement Audio
└── Function Call: Update Squad on Movement
```

### 🎯 On Reach Cover
```cpp
Custom Event: On Reach Cover
├── Input: Move Result (Enum)
├── Sequence: Movement Cleanup
│   ├── Clear Timer by Handle (MovementMonitorTimer)
│   ├── Function Call: Stop Movement Audio
│   └── Function Call: Reset Movement Speed
├── Branch: Movement Successful?
│   ├── Check Move Result == Success
│   ├── True → Custom Event: Establish Cover Position
│   └── False → Custom Event: Handle Movement Failed
└── Function Call: Log Movement Result
```

### 🛡️ Establish Cover Position
```cpp
Custom Event: Establish Cover Position
├── Sequence: Position Optimization
│   ├── Function Call: Fine-tune Cover Position
│   │   ├── Adjust for Optimal Cover Angle
│   │   ├── Ensure Clear Firing Lines
│   │   └── Optimize for Squad Coordination
│   ├── Function Call: Set Final Stance
│   │   ├── Analyze Cover Height
│   │   ├── Determine Optimal Stance
│   │   │   ├── High Cover → Standing
│   │   │   ├── Medium Cover → Crouching
│   │   │   └── Low Cover → Prone
│   │   └── Apply Stance Change
│   └── Function Call: Orient Toward Threat
├── Sequence: Cover Validation
│   ├── Function Call: Verify Cover Effectiveness
│   ├── Function Call: Check Escape Routes
│   ├── Function Call: Confirm Squad Coordination
│   └── Function Call: Test Firing Positions
├── Sequence: Success Completion
│   ├── Function Call: Broadcast Cover Established
│   ├── Function Call: Update Decision Logger
│   ├── Function Call: Play Cover Success Audio
│   └── Finish Execute (Success)
└── Function Call: Start Cover Monitoring
```

## 🔧 Core Functions

### 🎯 Score Cover Position
```cpp
Function: Score Cover Position
├── Input: Cover Location (Vector)
├── Output: Cover Score (Float)
├── Implementation:
│   ├── Variable: Total Score (Float) = 0.0
│   ├── Sequence: Distance Scoring
│   │   ├── Get Distance to Cover
│   │   ├── Calculate Distance Score
│   │   │   ├── Optimal Distance: 300-600 units
│   │   │   ├── Score: 1.0 - (Distance Deviation / Max Distance)
│   │   │   └── Clamp: 0.0 to 1.0
│   │   ├── Multiply: Distance Score × DistanceWeight
│   │   └── Add to Total Score
│   ├── Sequence: Cover Quality Scoring
│   │   ├── Function Call: Analyze Cover Height
│   │   ├── Function Call: Check Cover Material
│   │   ├── Function Call: Evaluate Cover Width
│   │   ├── Calculate Quality Score (0.0-1.0)
│   │   ├── Multiply: Quality Score × CoverQualityWeight
│   │   └── Add to Total Score
│   ├── Sequence: Line of Sight Scoring
│   │   ├── Line Trace to Enemy
│   │   ├── Branch: Has Partial LOS?
│   │   │   ├── True → LOS Score = 0.8
│   │   │   └── False → LOS Score = 0.2
│   │   ├── Multiply: LOS Score × LineOfSightWeight
│   │   └── Add to Total Score
│   ├── Sequence: Safety Scoring
│   │   ├── Multi Sphere Trace (Enemy Detection)
│   │   ├── Count Nearby Enemies
│   │   ├── Calculate Safety Score
│   │   │   └── 1.0 - (Enemy Count × 0.2)
│   │   ├── Multiply: Safety Score × SafetyWeight
│   │   └── Add to Total Score
│   └── Return: Total Score
```

### 🏠 Analyze Cover Height
```cpp
Function: Analyze Cover Height
├── Input: Cover Location (Vector)
├── Output: Cover Height Score (Float)
├── Implementation:
│   ├── Line Trace Upward
│   │   ├── Start: Cover Location
│   │   ├── End: Cover Location + (0,0,500)
│   │   ├── Channel: Visibility
│   │   └── Hit Result: Cover Top
│   ├── Calculate Cover Height
│   │   └── Hit Distance from Ground
│   ├── Branch: Height >= MinCoverHeight?
│   │   ├── False → Return 0.0
│   │   └── True → Continue
│   ├── Calculate Height Score
│   │   ├── Optimal Height: 150-200 units
│   │   ├── Score Formula: Min(Height/200, 1.0)
│   │   └── Bonus for Extra Height
│   └── Return: Height Score
```

### 🎯 Validate Cover Accessibility
```cpp
Function: Validate Cover Accessibility
├── Input: Cover Location (Vector)
├── Output: Is Accessible (Boolean)
├── Implementation:
│   ├── Sequence: Pathfinding Check
│   │   ├── Find Path to Location
│   │   │   ├── Start: Controlled Pawn Location
│   │   │   ├── End: Cover Location
│   │   │   └── Agent Radius: Pawn Radius
│   │   ├── Branch: Path Found?
│   │   │   ├── False → Return False
│   │   │   └── True → Continue
│   │   └── Check Path Length < MaxCoverDistance
│   ├── Sequence: Obstacle Check
│   │   ├── Line Trace to Cover
│   │   ├── Branch: Clear Path?
│   │   │   ├── False → Return False
│   │   │   └── True → Continue
│   │   └── Capsule Trace (Agent Size)
│   ├── Sequence: Environmental Hazards
│   │   ├── Check for Fall Damage
│   │   ├── Check for Environmental Damage
│   │   └── Check for Trap Zones
│   └── Return: True (All Checks Passed)
```

### 🚶 Monitor Movement Progress
```cpp
Function: Monitor Movement Progress
├── Implementation:
│   ├── Get Controlled Pawn Location
│   ├── Get Distance to Cover Location
│   ├── Branch: Still Moving?
│   │   ├── Check Pawn Velocity > 10.0
│   │   ├── False → Custom Event: Handle Movement Stuck
│   │   └── True → Continue
│   ├── Branch: Under Fire During Movement?
│   │   ├── Get Blackboard Value (IsUnderFire)
│   │   ├── True → Function Call: Adjust Movement Pattern
│   │   │   ├── Increase Movement Speed
│   │   │   ├── Add Evasive Maneuvers
│   │   │   └── Consider Alternative Cover
│   │   └── False → Continue Normal Movement
│   ├── Function Call: Update Movement Animation
│   └── Function Call: Check for Better Cover
│       ├── Quick EQS Query (Nearby Only)
│       ├── Branch: Better Cover Available?
│       │   ├── True → Consider Route Change
│       │   └── False → Continue Current Path
│       └── Update Squad on Progress
```

## 🎮 Event Receive Tick AI
```cpp
Event Receive Tick AI
├── Branch: Query In Progress?
│   ├── Get Variable: QueryInProgress
│   ├── True → Function Call: Monitor Query Progress
│   └── False → Skip Query Monitoring
├── Branch: Moving to Cover?
│   ├── Check AI Move Status
│   ├── True → Function Call: Monitor Movement
│   └── False → Skip Movement Monitoring
└── Branch: Debug Enabled?
    ├── True → Function Call: Update Debug Visualization
    │   ├── Draw Debug Sphere (Current Cover)
    │   ├── Draw Debug Line (Path to Cover)
    │   ├── Draw Debug Text (Cover Score)
    │   └── Draw Debug Spheres (All Potential Covers)
    └── False → (No Action)
```

## 📊 Input/Output Pin Configuration

### 🔌 Input Execution Pins
- **Execute**: Main execution entry point
- **Abort**: Cancel cover search and movement

### 🔌 Output Execution Pins
- **Success**: Cover found and reached successfully
- **Failed**: Unable to find suitable cover
- **In Progress**: Currently searching or moving to cover

### 📋 Input Data Pins
- **Owner Controller**: AI Controller reference
- **Controlled Pawn**: Character seeking cover
- **Threat Location**: Position to take cover from (optional override)

### 📋 Output Data Pins
- **Cover Location**: Final cover position selected
- **Cover Score**: Quality rating of selected cover
- **Search Time**: Time taken to find cover
- **Movement Time**: Time taken to reach cover

This Blueprint implementation provides a sophisticated cover-finding system with EQS integration, intelligent scoring, pathfinding validation, and comprehensive movement handling!
