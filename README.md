# SquadMateAI - Unreal Engine 5 AI Teammate System

A production-ready AI teammate system for PUBG-style 5v5 Team Deathmatch games built with Unreal Engine 5's AI framework.

## 🎯 Features

- **Advanced AI Controller**: Custom `ASquadMateAIController` with Behavior Trees and Blackboards
- **AIPerception Integration**: Vision and hearing for tactical awareness
- **EQS Queries**: Smart positioning, flanking routes, and cover detection
- **Squad Coordination**: 5v5 team management with role assignments
- **Tactical Behaviors**: Engage, Flank, Revive, Hold, Suppress, Peek
- **Modular Components**: Health, Inventory, Revival, Squad Role systems
- **Animation Support**: Full character animation with prone, crouch, peek mechanics
- **Performance Analytics**: Decision logging, K/D tracking, ELO ranking

## 📁 Project Structure

```
Source/SquadMateAI/
├── Public/
│   ├── AI/
│   │   ├── SquadMateAIController.h
│   │   ├── SquadMateCharacter.h
│   │   └── SquadManager.h
│   ├── Components/
│   │   ├── HealthComponent.h
│   │   ├── InventoryComponent.h
│   │   ├── ReviveComponent.h
│   │   ├── SquadRoleComponent.h
│   │   └── DecisionLoggerComponent.h
│   ├── BehaviorTree/
│   │   ├── Tasks/
│   │   │   ├── BTTask_FindCover.h
│   │   │   ├── BTTask_Flank.h
│   │   │   ├── BTTask_EngageEnemy.h
│   │   │   ├── BTTask_ReviveAlly.h
│   │   │   └── BTTask_HoldPosition.h
│   │   ├── Decorators/
│   │   │   ├── BTDecorator_CheckHealth.h
│   │   │   ├── BTDecorator_CheckAmmo.h
│   │   │   └── BTDecorator_CheckDistance.h
│   │   └── Services/
│   │       ├── BTService_TargetScan.h
│   │       ├── BTService_RoleCheck.h
│   │       └── BTService_DangerAwareness.h
│   └── UI/
│       ├── SquadHUD.h
│       └── MatchStatsWidget.h
└── Private/
    └── [Implementation files]

Content/
├── AI/
│   ├── Blackboards/
│   │   └── BB_SquadMate.uasset
│   ├── BehaviorTrees/
│   │   └── BT_SquadMate.uasset
│   └── EQS/
│       ├── EQS_FindCover.uasset
│       ├── EQS_FlankRoute.uasset
│       └── EQS_ZoneScoring.uasset
├── Characters/
│   ├── Victor/
│   │   ├── BP_SquadMateCharacter.uasset
│   │   └── ABP_Victor.uasset
│   └── Animations/
│       ├── AS_Combat.uasset
│       └── BS_Movement.uasset
└── UI/
    ├── WBP_SquadHUD.uasset
    └── WBP_MatchStats.uasset
```

## 🚀 Quick Start

1. Create new UE5 C++ project
2. Add AI module dependencies to Build.cs
3. Implement core classes and components
4. Create Behavior Tree and Blackboard assets
5. Set up EQS queries for tactical positioning
6. Configure squad roles and spawn points

## 🎮 Agent Roles

- **Support**: Healing and revival specialist
- **Assault**: Aggressive front-line fighter
- **Scout**: Information gathering and flanking
- **Anchor**: Defensive positioning and area denial
- **Sniper**: Long-range elimination and overwatch

## 🔧 Key Systems

### AI Controller Features
- Behavior Tree execution with custom tasks
- AIPerception for enemy detection
- Blackboard-driven decision making
- Squad communication integration

### Combat Mechanics
- Dynamic cover usage with peeking
- Intelligent flanking maneuvers
- Suppressive fire coordination
- Tactical retreat and regrouping

### Squad Coordination
- Role-based behavior adaptation
- Team callouts and pinging
- Coordinated movement patterns
- Revival priority management
