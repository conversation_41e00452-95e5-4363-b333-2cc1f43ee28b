# BB_SquadMate Blackboard Configuration

This document outlines the Blackboard configuration for SquadMate AI agents, defining all the keys used for decision-making and state management.

## Blackboard Keys Overview

The SquadMate Blackboard contains keys organized into logical categories for efficient AI decision-making.

## Target and Enemy Information

### TargetActor (Object - Actor)
- **Purpose**: Current primary enemy target
- **Default**: None
- **Usage**: Combat engagement, aiming, tactical decisions
- **Updated By**: AI Perception, Squad Manager

### LastKnownEnemyLocation (Vector)
- **Purpose**: Last confirmed position of enemy target
- **Default**: (0, 0, 0)
- **Usage**: Investigation, prediction, flanking
- **Updated By**: AI Perception, Target tracking

### EnemyCount (Integer)
- **Purpose**: Number of detected enemies in area
- **Default**: 0
- **Usage**: Tactical decision making, retreat conditions
- **Updated By**: AI Perception Service

### NearestEnemy (Object - Actor)
- **Purpose**: Closest detected enemy
- **Default**: None
- **Usage**: Immediate threat assessment
- **Updated By**: Target Scan Service

## Squad and Team Information

### SquadRole (Enum - ESquadRole)
- **Purpose**: Agent's assigned tactical role
- **Default**: Assault
- **Values**: Support, Assault, Scout, Anchor, Sniper
- **Usage**: Behavior tree branching, role-specific tasks
- **Updated By**: Squad Manager, Role Assigner

### SquadLeader (Object - Actor)
- **Purpose**: Reference to squad leader
- **Default**: None
- **Usage**: Formation following, command hierarchy
- **Updated By**: Squad Manager

### SquadMembers (Object - Actor Array)
- **Purpose**: List of all squad members
- **Default**: Empty Array
- **Usage**: Coordination, formation, support decisions
- **Updated By**: Squad Manager

### AllyToRevive (Object - Actor)
- **Purpose**: Squad member requiring revival
- **Default**: None
- **Usage**: Medical support, priority targeting
- **Updated By**: Health monitoring, Squad Manager

### FormationPosition (Vector)
- **Purpose**: Agent's assigned position in formation
- **Default**: (0, 0, 0)
- **Usage**: Movement coordination, positioning
- **Updated By**: Formation Manager

## Tactical State

### TacticState (Enum - ETacticState)
- **Purpose**: Current tactical behavior mode
- **Default**: Patrol
- **Values**: Patrol, Engage, Flank, Retreat, Revive, Hold, Suppress, Peek
- **Usage**: Primary behavior tree selector
- **Updated By**: AI Controller, Decision system

### IsInCombat (Boolean)
- **Purpose**: Whether agent is actively engaged in combat
- **Default**: False
- **Usage**: Behavior switching, animation states
- **Updated By**: Combat detection, Timer-based

### CombatIntensity (Float)
- **Purpose**: Current level of combat engagement (0.0-1.0)
- **Default**: 0.0
- **Usage**: Stress responses, accuracy modifiers
- **Updated By**: Combat analysis system

### ThreatLevel (Enum - EThreatLevel)
- **Purpose**: Assessed danger level of current situation
- **Default**: Low
- **Values**: None, Low, Medium, High, Critical
- **Usage**: Defensive behaviors, retreat decisions
- **Updated By**: Threat assessment service

## Movement and Positioning

### CoverLocation (Vector)
- **Purpose**: Target cover position
- **Default**: (0, 0, 0)
- **Usage**: Cover-seeking behavior, defensive positioning
- **Updated By**: EQS Cover Query, Manual assignment

### FlankLocation (Vector)
- **Purpose**: Flanking route destination
- **Default**: (0, 0, 0)
- **Usage**: Flanking maneuvers, tactical positioning
- **Updated By**: EQS Flank Query, Tactical planner

### PatrolRoute (Object - Actor Array)
- **Purpose**: Waypoints for patrol behavior
- **Default**: Empty Array
- **Usage**: Patrol movement, area coverage
- **Updated By**: Level design, Dynamic patrol system

### MovementSpeed (Float)
- **Purpose**: Current movement speed modifier
- **Default**: 1.0
- **Usage**: Animation blending, movement behavior
- **Updated By**: Stance system, Tactical state

### CurrentStance (Enum - ECharacterStance)
- **Purpose**: Agent's current physical stance
- **Default**: Standing
- **Values**: Standing, Crouching, Prone, PeekLeft, PeekRight
- **Usage**: Animation, collision, visibility
- **Updated By**: Stance controller, Tactical decisions

## Health and Status

### HealthPercentage (Float)
- **Purpose**: Current health as percentage (0.0-1.0)
- **Default**: 1.0
- **Usage**: Medical decisions, retreat conditions
- **Updated By**: Health Component

### IsInjured (Boolean)
- **Purpose**: Whether agent has taken significant damage
- **Default**: False
- **Usage**: Medical priority, behavior modification
- **Updated By**: Health Component

### IsDowned (Boolean)
- **Purpose**: Whether agent is incapacitated
- **Default**: False
- **Usage**: Revival requests, squad support
- **Updated By**: Health Component

### NeedsMedicalAttention (Boolean)
- **Purpose**: Whether agent requires healing
- **Default**: False
- **Usage**: Medical item usage, support requests
- **Updated By**: Health monitoring

## Weapon and Equipment

### AmmoCount (Integer)
- **Purpose**: Current ammunition in magazine
- **Default**: 30
- **Usage**: Reload decisions, engagement choices
- **Updated By**: Inventory Component

### ReserveAmmo (Integer)
- **Purpose**: Total ammunition available
- **Default**: 120
- **Usage**: Ammo conservation, resupply needs
- **Updated By**: Inventory Component

### CurrentWeapon (Enum - EWeaponType)
- **Purpose**: Currently equipped weapon type
- **Default**: AssaultRifle
- **Usage**: Combat behavior, range decisions
- **Updated By**: Inventory Component

### IsReloading (Boolean)
- **Purpose**: Whether agent is currently reloading
- **Default**: False
- **Usage**: Vulnerability assessment, timing
- **Updated By**: Weapon system

### HasGrenades (Boolean)
- **Purpose**: Whether agent has explosive ordnance
- **Default**: False
- **Usage**: Tactical options, area denial
- **Updated By**: Inventory Component

## Environmental Awareness

### VisibleEnemies (Object - Actor Array)
- **Purpose**: All currently visible enemy targets
- **Default**: Empty Array
- **Usage**: Target prioritization, threat assessment
- **Updated By**: AI Perception

### NearbyAllies (Object - Actor Array)
- **Purpose**: Squad members within communication range
- **Default**: Empty Array
- **Usage**: Coordination, formation maintenance
- **Updated By**: Proximity detection

### CoverPoints (Vector Array)
- **Purpose**: Known cover positions in area
- **Default**: Empty Array
- **Usage**: Tactical planning, fallback options
- **Updated By**: Environment analysis

### DangerZones (Vector Array)
- **Purpose**: Areas to avoid due to threats
- **Default**: Empty Array
- **Usage**: Path planning, safety assessment
- **Updated By**: Threat mapping

## Objectives and Goals

### CurrentObjective (Object - Actor)
- **Purpose**: Primary mission objective
- **Default**: None
- **Usage**: Goal-oriented behavior, priority decisions
- **Updated By**: Mission system, Squad Manager

### ObjectiveLocation (Vector)
- **Purpose**: Target location for current objective
- **Default**: (0, 0, 0)
- **Usage**: Navigation, completion checking
- **Updated By**: Objective system

### ObjectivePriority (Float)
- **Purpose**: Importance of current objective (0.0-1.0)
- **Default**: 0.5
- **Usage**: Decision weighting, behavior priority
- **Updated By**: Mission system

## Communication and Coordination

### LastCalloutTime (Float)
- **Purpose**: Timestamp of last communication
- **Default**: 0.0
- **Usage**: Communication cooldowns, spam prevention
- **Updated By**: Communication system

### PendingOrders (String Array)
- **Purpose**: Queued commands from squad leader
- **Default**: Empty Array
- **Usage**: Command execution, priority management
- **Updated By**: Command system

### SharedIntel (String)
- **Purpose**: Information shared with squad
- **Default**: ""
- **Usage**: Tactical awareness, coordination
- **Updated By**: Intelligence system

## Debug and Development

### DebugTarget (Object - Actor)
- **Purpose**: Manual target assignment for testing
- **Default**: None
- **Usage**: Development, debugging
- **Updated By**: Debug commands

### DebugMode (Boolean)
- **Purpose**: Enable debug visualization
- **Default**: False
- **Usage**: Development tools, troubleshooting
- **Updated By**: Debug system

### LoggingEnabled (Boolean)
- **Purpose**: Enable decision logging
- **Default**: True
- **Usage**: Performance analysis, behavior tracking
- **Updated By**: Logging system

## Key Update Frequencies

| Key Category | Update Frequency | Performance Impact |
|--------------|------------------|-------------------|
| Target Information | Every 0.1s | Medium |
| Squad Information | Every 0.5s | Low |
| Tactical State | Every 0.2s | Medium |
| Health Status | Every 0.1s | Low |
| Weapon Status | Every 0.1s | Low |
| Environmental | Every 1.0s | High |
| Objectives | Every 2.0s | Low |
| Communication | Event-driven | Low |

## Memory Optimization

### Key Priorities
- **Critical**: TargetActor, TacticState, HealthPercentage
- **Important**: SquadRole, CoverLocation, AmmoCount
- **Optional**: Debug keys, Historical data

### Cleanup Rules
- Clear target references when out of range
- Reset tactical states on completion
- Purge old environmental data
- Limit array sizes for performance
