# BTTask_FireWeapon - Complete Blueprint Implementation

## 🎯 Blueprint Class Setup

### Class Configuration
- **Parent Class**: `BTTaskNode` (or `BTTask_BlueprintBase`)
- **Blueprint Name**: `BTTask_FireWeapon_BP`
- **Location**: `/Content/AI/BehaviorTree/Tasks/`
- **Category**: "SquadMate AI Tasks"

## 📋 Blueprint Variables

### 🔑 Blackboard Key Selectors
```cpp
// Input Pins (Category: Blackboard Keys)
TargetActor (Blackboard Key Selector)
├── Key Type: Object (Actor)
├── Default: "TargetActor"
└── Tooltip: "Enemy target to engage"

HasLineOfSight (Blackboard Key Selector)
├── Key Type: Bool
├── Default: "HasLineOfSight"
└── Tooltip: "Whether agent can see target"

AmmoCount (Blackboard Key Selector)
├── Key Type: Int
├── Default: "AmmoCount"
└── Tooltip: "Current ammunition count"

IsReloading (Blackboard Key Selector)
├── Key Type: Bool
├── Default: "IsReloading"
└── Tooltip: "Whether currently reloading"

WeaponType (Blackboard Key Selector)
├── Key Type: Enum
├── Default: "WeaponType"
└── Tooltip: "Current weapon type"
```

### ⚙️ Fire Control Settings
```cpp
// Fire Control (Category: Fire Control)
FireMode (Enum: EFireMode) = BurstFire
├── Values: SingleShot, BurstFire, FullAuto, Suppressive
└── Tooltip: "Weapon firing mode"

FireRate (Float) = 0.1
├── Meta: (ClampMin="0.05", ClampMax="2.0")
└── Tooltip: "Time between shots in seconds"

BurstSize (Integer) = 3
├── Meta: (ClampMin="1", ClampMax="10")
└── Tooltip: "Number of shots per burst"

BurstCooldown (Float) = 0.5
├── Meta: (ClampMin="0.1", ClampMax="3.0")
└── Tooltip: "Delay between bursts"

MaxFireDuration (Float) = 5.0
├── Meta: (ClampMin="1.0", ClampMax="30.0")
└── Tooltip: "Maximum continuous fire time"

AccuracyModifier (Float) = 0.8
├── Meta: (ClampMin="0.1", ClampMax="1.0")
└── Tooltip: "Base accuracy multiplier"
```

### 🎯 Engagement Parameters
```cpp
// Engagement (Category: Engagement)
MinEngagementRange (Float) = 50.0
├── Meta: (ClampMin="10.0", ClampMax="500.0")
└── Tooltip: "Minimum distance to engage"

MaxEngagementRange (Float) = 1000.0
├── Meta: (ClampMin="100.0", ClampMax="2000.0")
└── Tooltip: "Maximum effective range"

OptimalRange (Float) = 400.0
├── Meta: (ClampMin="50.0", ClampMax="1500.0")
└── Tooltip: "Preferred engagement distance"

PredictionFactor (Float) = 0.5
├── Meta: (ClampMin="0.0", ClampMax="2.0")
└── Tooltip: "Target movement prediction"

RequireLineOfSight (Boolean) = true
└── Tooltip: "Must have clear line of sight"
```

### 🎬 Animation & Effects
```cpp
// Animation (Category: Animation)
FireMontage (Animation Montage)
└── Tooltip: "Animation for weapon firing"

AimOffset (Aim Offset)
└── Tooltip: "Aiming animation offset"

MuzzleFlashEffect (Particle System)
└── Tooltip: "Muzzle flash particle effect"

FireSound (Sound Base)
└── Tooltip: "Weapon fire audio"

ShellEjectSound (Sound Base)
└── Tooltip: "Shell casing eject audio"
```

### 🎮 Stance Control
```cpp
// Stance (Category: Stance)
AutoAdjustStance (Boolean) = true
└── Tooltip: "Automatically optimize stance"

PreferCrouchedFire (Boolean) = false
└── Tooltip: "Prefer crouched firing position"

StanceForRange (Map: Float → Enum)
├── 0-200: Standing
├── 200-600: Crouching
└── 600+: Prone
```

## 🔧 Event Graph Implementation

### 📍 Event Receive Execute AI
```cpp
Event Receive Execute AI
├── Sequence: Input Validation
│   ├── Get Blackboard Component
│   ├── Get Controlled Pawn
│   ├── Get Blackboard Value as Object (TargetActor)
│   ├── Branch: Has Valid Target?
│   │   ├── False → Finish Execute (Failed)
│   │   └── True → Continue
│   └── Function Call: Validate Engagement Conditions
├── Branch: Can Engage Target?
│   ├── False → Finish Execute (Failed)
│   └── True → Continue
├── Custom Event: Initialize Fire Sequence
└── Return Value: In Progress
```

### 🎯 Initialize Fire Sequence
```cpp
Custom Event: Initialize Fire Sequence
├── Sequence: Setup Fire State
│   ├── Function Call: Get Current Weapon Info
│   ├── Function Call: Calculate Optimal Stance
│   ├── Function Call: Set Agent Stance
│   ├── Set Variable: FireStartTime (Current Time)
│   ├── Set Variable: ShotsFiredInBurst (0)
│   └── Set Variable: IsCurrentlyFiring (True)
├── Sequence: Begin Aiming
│   ├── Function Call: Calculate Initial Aim Point
│   ├── Function Call: Start Aim Animation
│   ├── Function Call: Play Weapon Ready Sound
│   └── Set Timer by Function Name
│       ├── Function Name: "Update Aim Tracking"
│       ├── Time: 0.05 (High frequency aiming)
│       ├── Looping: True
│       └── Store Handle: AimUpdateTimer
├── Sequence: Start Fire Pattern
│   ├── Switch on Enum (FireMode)
│   │   ├── SingleShot → Custom Event: Start Single Shot
│   │   ├── BurstFire → Custom Event: Start Burst Fire
│   │   ├── FullAuto → Custom Event: Start Full Auto
│   │   └── Suppressive → Custom Event: Start Suppressive Fire
│   └── Set Timer by Function Name
│       ├── Function Name: "Monitor Fire Completion"
│       ├── Time: 0.1
│       ├── Looping: True
│       └── Store Handle: FireMonitorTimer
└── Function Call: Broadcast Fire Start
```

### 🔫 Start Burst Fire (Primary Mode)
```cpp
Custom Event: Start Burst Fire
├── Sequence: Burst Setup
│   ├── Set Variable: CurrentBurstShots (0)
│   ├── Set Variable: InBurstMode (True)
│   └── Function Call: Calculate Burst Aim Points
├── Set Timer by Function Name
│   ├── Function Name: "Execute Burst Shot"
│   ├── Time: FireRate
│   ├── Looping: True
│   └── Store Handle: BurstFireTimer
└── Function Call: Log Fire Mode Start
```

### 💥 Execute Burst Shot
```cpp
Function: Execute Burst Shot
├── Sequence: Pre-Shot Validation
│   ├── Function Call: Check Ammo Available
│   ├── Branch: Has Ammo?
│   │   ├── False → Custom Event: Handle Out of Ammo
│   │   └── True → Continue
│   ├── Function Call: Check Line of Sight
│   ├── Branch: Can See Target?
│   │   ├── False → Custom Event: Handle Lost Target
│   │   └── True → Continue
│   └── Function Call: Validate Target Still Alive
├── Sequence: Execute Shot
│   ├── Function Call: Calculate Current Aim Point
│   ├── Function Call: Apply Accuracy Spread
│   ├── Function Call: Fire Single Shot
│   ├── Function Call: Apply Recoil
│   ├── Function Call: Update Ammo Count
│   └── Add to Variable: CurrentBurstShots (+1)
├── Sequence: Burst Management
│   ├── Branch: Burst Complete?
│   │   ├── CurrentBurstShots >= BurstSize
│   │   ├── True → Custom Event: Complete Burst
│   │   └── False → Continue Burst
│   └── Function Call: Update Fire Animation
└── Function Call: Update Decision Logger
```

### ✅ Complete Burst
```cpp
Custom Event: Complete Burst
├── Sequence: Burst Cleanup
│   ├── Clear Timer by Handle (BurstFireTimer)
│   ├── Set Variable: InBurstMode (False)
│   ├── Set Variable: CurrentBurstShots (0)
│   └── Add to Variable: ShotsFiredInBurst (+BurstSize)
├── Sequence: Cooldown Management
│   ├── Branch: More Bursts Needed?
│   │   ├── Check Target Still Valid
│   │   ├── Check Ammo Remaining
│   │   ├── Check Fire Duration
│   │   ├── True → Set Timer by Function Name
│   │   │   ├── Function Name: "Start Burst Fire"
│   │   │   ├── Time: BurstCooldown
│   │   │   └── Looping: False
│   │   └── False → Custom Event: Complete Fire Sequence
│   └── Function Call: Play Burst Complete Sound
└── Function Call: Update Weapon Heat
```

## 🔧 Core Functions

### 🎯 Calculate Current Aim Point
```cpp
Function: Calculate Current Aim Point
├── Input: Target Actor (Actor Reference)
├── Output: Aim Point (Vector)
├── Implementation:
│   ├── Get Target Current Location
│   ├── Branch: Use Prediction?
│   │   ├── True → Function Call: Predict Target Movement
│   │   │   ├── Get Target Velocity
│   │   │   ├── Calculate Flight Time
│   │   │   │   └── Distance / Projectile Speed
│   │   │   ├── Multiply: Velocity × Flight Time × PredictionFactor
│   │   │   └── Add to Current Location
│   │   └── False → Use Current Location
│   ├── Function Call: Apply Accuracy Spread
│   │   ├── Get Current Accuracy
│   │   ├── Calculate Spread Radius
│   │   ├── Generate Random Offset in Cone
│   │   └── Add Offset to Predicted Location
│   └── Function Call: Validate Aim Point
│       ├── Check for Obstacles
│       ├── Ensure Within Weapon Range
│       └── Adjust for Agent Height
└── Return: Final Aim Point
```

### 🔥 Fire Single Shot
```cpp
Function: Fire Single Shot
├── Input: Aim Point (Vector)
├── Implementation:
│   ├── Sequence: Weapon Fire
│   │   ├── Get Weapon Muzzle Location
│   │   ├── Line Trace by Channel
│   │   │   ├── Start: Muzzle Location
│   │   │   ├── End: Aim Point
│   │   │   ├── Channel: Visibility
│   │   │   └── Ignore: Self + Allies
│   │   ├── Branch: Hit Target?
│   │   │   ├── True → Function Call: Apply Damage
│   │   │   └── False → Function Call: Process Miss
│   │   └── Function Call: Create Bullet Trail
│   ├── Sequence: Visual Effects
│   │   ├── Spawn Emitter at Location
│   │   │   ├── Template: MuzzleFlashEffect
│   │   │   ├── Location: Muzzle Location
│   │   │   └── Auto Destroy: True
│   │   ├── Play Sound at Location
│   │   │   ├── Sound: FireSound
│   │   │   └── Location: Muzzle Location
│   │   └── Delay (0.1) → Play Sound (ShellEjectSound)
│   ├── Sequence: Animation
│   │   ├── Play Animation Montage (FireMontage)
│   │   ├── Trigger Animation Notify (Fire)
│   │   └── Update Aim Offset
│   └── Sequence: State Updates
│       ├── Subtract from Blackboard (AmmoCount, 1)
│       ├── Set Variable: LastShotTime (Current Time)
│       └── Function Call: Update Weapon Statistics
```

### 🎯 Apply Damage
```cpp
Function: Apply Damage
├── Input: Hit Actor (Actor), Hit Location (Vector)
├── Implementation:
│   ├── Cast to Character
│   ├── Branch: Is Valid Character?
│   │   ├── True → Continue
│   │   └── False → Return
│   ├── Get Weapon Damage Value
│   ├── Apply Damage Modifiers
│   │   ├── Distance Modifier
│   │   ├── Hit Location Modifier (Head/Body/Limb)
│   │   └── Armor Penetration
│   ├── Apply Damage
│   │   ├── Damage Amount: Modified Damage
│   │   ├── Damage Type: Bullet Damage
│   │   ├── Hit Location: Impact Point
│   │   └── Instigator: Controlled Pawn
│   ├── Function Call: Create Hit Effect
│   │   ├── Blood Splatter
│   │   ├── Impact Decal
│   │   └── Hit Sound
│   └── Function Call: Log Successful Hit
```

### 🔄 Update Aim Tracking
```cpp
Function: Update Aim Tracking
├── Implementation:
│   ├── Get Current Target
│   ├── Branch: Has Valid Target?
│   │   ├── False → Custom Event: Handle Lost Target
│   │   └── True → Continue
│   ├── Function Call: Calculate Current Aim Point
│   ├── Function Call: Get Agent Look Rotation
│   ├── Function Call: Calculate Required Rotation
│   │   └── Find Look at Rotation (Agent → Aim Point)
│   ├── Function Call: Interpolate Rotation
│   │   ├── Current: Agent Rotation
│   │   ├── Target: Required Rotation
│   │   ├── Speed: Aim Speed
│   │   └── Delta Time: Get World Delta Seconds
│   ├── Set Actor Rotation (Interpolated)
│   └── Function Call: Update Aim Animation
│       ├── Calculate Aim Offset Values
│       ├── Set Animation Variables
│       └── Update Blend Space Input
```

## 🎮 Event Receive Tick AI
```cpp
Event Receive Tick AI
├── Branch: Is Currently Firing?
│   ├── Get Variable: IsCurrentlyFiring
│   ├── False → Return (Skip Tick)
│   └── True → Continue
├── Sequence: Continuous Monitoring
│   ├── Function Call: Monitor Fire Duration
│   │   ├── Get Current Time
│   │   ├── Subtract: FireStartTime
│   │   ├── Branch: Exceeded Max Duration?
│   │   │   ├── True → Custom Event: Force Fire Stop
│   │   │   └── False → Continue
│   │   └── Update Fire Progress
│   ├── Function Call: Monitor Target Status
│   │   ├── Check Target Health
│   │   ├── Check Target Distance
│   │   └── Check Line of Sight
│   ├── Function Call: Monitor Agent Status
│   │   ├── Check Agent Health
│   │   ├── Check Ammo Count
│   │   └── Check Stance Stability
│   └── Function Call: Update Fire Effectiveness
│       ├── Calculate Hit Rate
│       ├── Adjust Accuracy
│       └── Optimize Fire Pattern
└── Branch: Debug Enabled?
    ├── True → Function Call: Update Debug Display
    └── False → (No Action)
```

## 📊 Input/Output Pin Configuration

### 🔌 Input Execution Pins
- **Execute**: Main execution entry point
- **Abort**: Emergency stop firing

### 🔌 Output Execution Pins
- **Success**: Target eliminated or engagement complete
- **Failed**: Unable to engage or critical failure
- **In Progress**: Currently engaging target

### 📋 Input Data Pins
- **Owner Controller**: AI Controller reference
- **Controlled Pawn**: Character performing the action
- **Target Actor**: Enemy to engage (from Blackboard)
- **Fire Duration**: Override for maximum fire time

### 📋 Output Data Pins
- **Shots Fired**: Total number of shots fired
- **Hits Scored**: Number of successful hits
- **Accuracy Rate**: Percentage of shots that hit
- **Engagement Time**: Total time spent firing

This Blueprint implementation provides a complete, production-ready weapon firing system with multiple fire modes, advanced aiming, realistic ballistics, and comprehensive feedback systems!
