#pragma once

#include "CoreMinimal.h"
#include "GameFramework/HUD.h"
#include "AI/SquadMateAIController.h"
#include "AI/SquadManager.h"
#include "Components/DecisionLoggerComponent.h"
#include "Engine/Canvas.h"
#include "SquadHUD.generated.h"

USTRUCT(BlueprintType)
struct FAgentDisplayInfo
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    ASquadMateAIController* Agent = nullptr;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FVector ScreenPosition = FVector::ZeroVector;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FString DisplayText;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FLinearColor DisplayColor = FLinearColor::White;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    bool bIsVisible = true;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float HealthPercentage = 1.0f;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    ESquadRole Role = ESquadRole::Assault;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    ETacticState CurrentTactic = ETacticState::Patrol;

    FAgentDisplayInfo()
    {
        Agent = nullptr;
        ScreenPosition = FVector::ZeroVector;
        DisplayText = TEXT("");
        DisplayColor = FLinearColor::White;
        bIsVisible = true;
        HealthPercentage = 1.0f;
        Role = ESquadRole::Assault;
        CurrentTactic = ETacticState::Patrol;
    }
};

USTRUCT(BlueprintType)
struct FSquadHUDSettings
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bShowAgentInfo = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bShowHealthBars = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bShowTacticState = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bShowRoleIcons = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bShowDecisionHistory = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bShowPerformanceMetrics = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bShowSquadFormation = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float MaxDisplayDistance = 2000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float InfoDisplayDuration = 3.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FLinearColor FriendlyColor = FLinearColor::Green;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FLinearColor EnemyColor = FLinearColor::Red;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FLinearColor NeutralColor = FLinearColor::Yellow;

    FSquadHUDSettings()
    {
        bShowAgentInfo = true;
        bShowHealthBars = true;
        bShowTacticState = true;
        bShowRoleIcons = true;
        bShowDecisionHistory = false;
        bShowPerformanceMetrics = false;
        bShowSquadFormation = true;
        MaxDisplayDistance = 2000.0f;
        InfoDisplayDuration = 3.0f;
        FriendlyColor = FLinearColor::Green;
        EnemyColor = FLinearColor::Red;
        NeutralColor = FLinearColor::Yellow;
    }
};

/**
 * HUD class for displaying SquadMate AI information and debug data
 * Shows agent status, decisions, performance metrics, and tactical information
 */
UCLASS(BlueprintType, Blueprintable)
class SQUADMATEAI_API ASquadHUD : public AHUD
{
    GENERATED_BODY()

public:
    ASquadHUD();

protected:
    virtual void BeginPlay() override;
    virtual void DrawHUD() override;
    virtual void Tick(float DeltaTime) override;

    // HUD Configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HUD Settings")
    FSquadHUDSettings HUDSettings;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HUD Settings")
    bool bHUDEnabled = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HUD Settings")
    float UpdateFrequency = 0.1f;

    // Font Settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Display")
    UFont* DefaultFont = nullptr;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Display")
    UFont* LargeFont = nullptr;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Display")
    UFont* SmallFont = nullptr;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Display")
    float FontScale = 1.0f;

    // Colors and Styling
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Display")
    FLinearColor BackgroundColor = FLinearColor(0.0f, 0.0f, 0.0f, 0.5f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Display")
    FLinearColor BorderColor = FLinearColor::White;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Display")
    float BorderThickness = 2.0f;

    // Squad Manager Reference
    UPROPERTY(BlueprintReadWrite, Category = "Squad")
    ASquadManager* SquadManager = nullptr;

    // Display Data
    UPROPERTY(BlueprintReadOnly, Category = "Display")
    TArray<FAgentDisplayInfo> AgentDisplayData;

    UPROPERTY(BlueprintReadOnly, Category = "Display")
    TMap<ASquadMateAIController*, FString> RecentDecisions;

    // Performance Tracking
    UPROPERTY(BlueprintReadOnly, Category = "Performance")
    float AverageFrameTime = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Performance")
    int32 ActiveAgentCount = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Performance")
    float SquadCohesion = 0.0f;

public:
    // Public Interface
    UFUNCTION(BlueprintCallable, Category = "HUD")
    void SetHUDEnabled(bool bEnabled);

    UFUNCTION(BlueprintCallable, Category = "HUD")
    void ToggleHUD();

    UFUNCTION(BlueprintCallable, Category = "HUD")
    void SetSquadManager(ASquadManager* Manager);

    UFUNCTION(BlueprintCallable, Category = "HUD")
    void UpdateHUDSettings(const FSquadHUDSettings& NewSettings);

    // Display Control
    UFUNCTION(BlueprintCallable, Category = "Display")
    void ShowAgentInfo(bool bShow);

    UFUNCTION(BlueprintCallable, Category = "Display")
    void ShowHealthBars(bool bShow);

    UFUNCTION(BlueprintCallable, Category = "Display")
    void ShowTacticStates(bool bShow);

    UFUNCTION(BlueprintCallable, Category = "Display")
    void ShowRoleIcons(bool bShow);

    UFUNCTION(BlueprintCallable, Category = "Display")
    void ShowDecisionHistory(bool bShow);

    UFUNCTION(BlueprintCallable, Category = "Display")
    void ShowPerformanceMetrics(bool bShow);

    // Agent Tracking
    UFUNCTION(BlueprintCallable, Category = "Agents")
    void AddAgentToDisplay(ASquadMateAIController* Agent);

    UFUNCTION(BlueprintCallable, Category = "Agents")
    void RemoveAgentFromDisplay(ASquadMateAIController* Agent);

    UFUNCTION(BlueprintCallable, Category = "Agents")
    void ClearAgentDisplay();

    UFUNCTION(BlueprintCallable, Category = "Agents")
    void RefreshAgentList();

    // Utility Functions
    UFUNCTION(BlueprintCallable, Category = "Utility")
    FVector WorldToScreenPosition(const FVector& WorldLocation) const;

    UFUNCTION(BlueprintCallable, Category = "Utility")
    bool IsLocationOnScreen(const FVector& WorldLocation) const;

    UFUNCTION(BlueprintCallable, Category = "Utility")
    float GetDistanceToPlayer(const FVector& WorldLocation) const;

protected:
    // Core Drawing Functions
    void DrawAgentInformation();
    void DrawSquadOverview();
    void DrawPerformanceMetrics();
    void DrawDecisionHistory();
    void DrawFormationDisplay();

    // Agent-Specific Drawing
    void DrawAgentHealthBar(const FAgentDisplayInfo& AgentInfo, const FVector2D& Position);
    void DrawAgentTacticState(const FAgentDisplayInfo& AgentInfo, const FVector2D& Position);
    void DrawAgentRoleIcon(const FAgentDisplayInfo& AgentInfo, const FVector2D& Position);
    void DrawAgentDecisionText(const FAgentDisplayInfo& AgentInfo, const FVector2D& Position);

    // UI Helper Functions
    void DrawTextWithBackground(const FString& Text, const FVector2D& Position, 
                              const FLinearColor& TextColor, const FLinearColor& BackgroundColor,
                              UFont* Font = nullptr);

    void DrawProgressBar(const FVector2D& Position, const FVector2D& Size, 
                        float Progress, const FLinearColor& FillColor, 
                        const FLinearColor& BackgroundColor = FLinearColor::Black);

    void DrawBox(const FVector2D& Position, const FVector2D& Size, 
                const FLinearColor& Color, bool bFilled = true);

    void DrawLine(const FVector2D& Start, const FVector2D& End, 
                 const FLinearColor& Color, float Thickness = 1.0f);

    // Data Update Functions
    void UpdateAgentDisplayData();
    void UpdatePerformanceData();
    void UpdateDecisionHistory();
    void GatherSquadInformation();

    // Utility Functions
    FString GetTacticStateDisplayName(ETacticState State) const;
    FString GetRoleDisplayName(ESquadRole Role) const;
    FLinearColor GetTacticStateColor(ETacticState State) const;
    FLinearColor GetRoleColor(ESquadRole Role) const;
    FLinearColor GetHealthColor(float HealthPercentage) const;

    // Screen Space Calculations
    FVector2D CalculateAgentInfoPosition(const FAgentDisplayInfo& AgentInfo) const;
    bool ShouldDisplayAgent(const FAgentDisplayInfo& AgentInfo) const;
    float CalculateDisplayPriority(const FAgentDisplayInfo& AgentInfo) const;

    // Performance Optimization
    void OptimizeDisplayUpdates();
    bool ShouldUpdateThisFrame() const;

private:
    // Internal State
    float LastUpdateTime = 0.0f;
    float LastPerformanceUpdate = 0.0f;
    int32 FrameCounter = 0;
    
    // Cached Data
    TArray<ASquadMateAIController*> CachedAgents;
    TMap<ASquadMateAIController*, float> LastDecisionTimes;
    
    // Display Optimization
    const float MinDisplayUpdateInterval = 0.05f;
    const float PerformanceUpdateInterval = 1.0f;
    const int32 MaxDisplayedAgents = 10;
    
    // Helper Functions
    void InitializeHUD();
    void CleanupDisplayData();
    bool IsAgentValid(ASquadMateAIController* Agent) const;
    void SortAgentsByPriority();
};
