#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "GameplayTagContainer.h"
#include "Engine/World.h"
#include "DecisionLoggerComponent.generated.h"

UENUM(BlueprintType)
enum class EDecisionType : uint8
{
    Movement        UMETA(DisplayName = "Movement"),
    Combat          UMETA(DisplayName = "Combat"),
    Tactical        UMETA(DisplayName = "Tactical"),
    Communication   UMETA(DisplayName = "Communication"),
    Survival        UMETA(DisplayName = "Survival"),
    Objective       UMETA(DisplayName = "Objective"),
    Formation       UMETA(DisplayName = "Formation"),
    Support         UMETA(DisplayName = "Support")
};

UENUM(BlueprintType)
enum class EDecisionResult : uint8
{
    Success         UMETA(DisplayName = "Success"),
    Failure         UMETA(DisplayName = "Failure"),
    Partial         UMETA(DisplayName = "Partial Success"),
    Interrupted     UMETA(DisplayName = "Interrupted"),
    Timeout         UMETA(DisplayName = "Timeout"),
    Cancelled       UMETA(DisplayName = "Cancelled")
};

USTRUCT(BlueprintType)
struct FDecisionLogEntry
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float Timestamp = 0.0f;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FString DecisionName;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    EDecisionType DecisionType;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FVector WorldLocation;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FString Context;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    EDecisionResult Result;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float ExecutionTime = 0.0f;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float SuccessScore = 0.0f;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    TMap<FString, FString> Parameters;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FGameplayTagContainer DecisionTags;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FString AgentID;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FString MatchID;

    FDecisionLogEntry()
    {
        Timestamp = 0.0f;
        DecisionName = TEXT("Unknown");
        DecisionType = EDecisionType::Movement;
        WorldLocation = FVector::ZeroVector;
        Context = TEXT("");
        Result = EDecisionResult::Success;
        ExecutionTime = 0.0f;
        SuccessScore = 0.0f;
        AgentID = TEXT("");
        MatchID = TEXT("");
    }
};

USTRUCT(BlueprintType)
struct FPerformanceMetrics
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    int32 TotalDecisions = 0;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    int32 SuccessfulDecisions = 0;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    int32 FailedDecisions = 0;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float AverageExecutionTime = 0.0f;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float AverageSuccessScore = 0.0f;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    TMap<EDecisionType, int32> DecisionTypeCount;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    TMap<EDecisionType, float> DecisionTypeSuccessRate;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float SessionStartTime = 0.0f;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float SessionDuration = 0.0f;

    FPerformanceMetrics()
    {
        TotalDecisions = 0;
        SuccessfulDecisions = 0;
        FailedDecisions = 0;
        AverageExecutionTime = 0.0f;
        AverageSuccessScore = 0.0f;
        SessionStartTime = 0.0f;
        SessionDuration = 0.0f;
    }
};

DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnDecisionLogged, const FDecisionLogEntry&, LogEntry);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnPerformanceUpdated, const FPerformanceMetrics&, Metrics);

UCLASS(ClassGroup=(Custom), meta=(BlueprintSpawnableComponent), BlueprintType, Blueprintable)
class SQUADMATEAI_API UDecisionLoggerComponent : public UActorComponent
{
    GENERATED_BODY()

public:
    UDecisionLoggerComponent();

protected:
    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;
    virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

    // Logging Configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Logging")
    bool bEnableLogging = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Logging")
    bool bLogToFile = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Logging")
    bool bLogToConsole = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Logging")
    FString LogFileName = TEXT("SquadMateDecisions");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Logging")
    FString LogDirectory = TEXT("Logs/SquadMateAI");

    // Memory Management
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory")
    int32 MaxLogEntries = 1000;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory")
    bool bAutoFlushToFile = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory")
    float AutoFlushInterval = 30.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory")
    int32 FlushBatchSize = 100;

    // Performance Tracking
    UPROPERTY(BlueprintReadOnly, Category = "Performance")
    FPerformanceMetrics CurrentMetrics;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bTrackPerformanceMetrics = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float MetricsUpdateInterval = 5.0f;

    // Data Storage
    UPROPERTY(BlueprintReadOnly, Category = "Data")
    TArray<FDecisionLogEntry> LogEntries;

    UPROPERTY(BlueprintReadOnly, Category = "Data")
    FString AgentID;

    UPROPERTY(BlueprintReadOnly, Category = "Data")
    FString CurrentMatchID;

    // Events
    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnDecisionLogged OnDecisionLogged;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnPerformanceUpdated OnPerformanceUpdated;

    // Filtering
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Filtering")
    TArray<EDecisionType> FilteredDecisionTypes;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Filtering")
    float MinSuccessScoreToLog = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Filtering")
    bool bLogOnlyFailures = false;

public:
    // Primary Logging Interface
    UFUNCTION(BlueprintCallable, Category = "Logging")
    void LogDecision(const FString& DecisionName, const FVector& Location, bool bSuccess = true, 
                    const FString& Context = TEXT(""), EDecisionType Type = EDecisionType::Movement);

    UFUNCTION(BlueprintCallable, Category = "Logging")
    void LogDecisionWithDetails(const FDecisionLogEntry& LogEntry);

    UFUNCTION(BlueprintCallable, Category = "Logging")
    void LogDecisionStart(const FString& DecisionName, const FVector& Location, 
                         EDecisionType Type = EDecisionType::Movement, const FString& Context = TEXT(""));

    UFUNCTION(BlueprintCallable, Category = "Logging")
    void LogDecisionEnd(const FString& DecisionName, EDecisionResult Result, 
                       float SuccessScore = 1.0f, const TMap<FString, FString>& Parameters = TMap<FString, FString>());

    // Batch Logging
    UFUNCTION(BlueprintCallable, Category = "Logging")
    void LogMultipleDecisions(const TArray<FDecisionLogEntry>& Entries);

    UFUNCTION(BlueprintCallable, Category = "Logging")
    void FlushLogsToFile();

    UFUNCTION(BlueprintCallable, Category = "Logging")
    void ClearLogs();

    // Data Retrieval
    UFUNCTION(BlueprintCallable, Category = "Data")
    TArray<FDecisionLogEntry> GetLogEntries() const { return LogEntries; }

    UFUNCTION(BlueprintCallable, Category = "Data")
    TArray<FDecisionLogEntry> GetLogEntriesByType(EDecisionType Type) const;

    UFUNCTION(BlueprintCallable, Category = "Data")
    TArray<FDecisionLogEntry> GetLogEntriesByTimeRange(float StartTime, float EndTime) const;

    UFUNCTION(BlueprintCallable, Category = "Data")
    TArray<FDecisionLogEntry> GetRecentLogEntries(float TimeWindow = 60.0f) const;

    UFUNCTION(BlueprintCallable, Category = "Data")
    FDecisionLogEntry GetLastLogEntry() const;

    // Performance Analysis
    UFUNCTION(BlueprintCallable, Category = "Performance")
    FPerformanceMetrics GetPerformanceMetrics() const { return CurrentMetrics; }

    UFUNCTION(BlueprintCallable, Category = "Performance")
    float GetSuccessRate(EDecisionType Type = EDecisionType::Movement) const;

    UFUNCTION(BlueprintCallable, Category = "Performance")
    float GetAverageExecutionTime(EDecisionType Type = EDecisionType::Movement) const;

    UFUNCTION(BlueprintCallable, Category = "Performance")
    int32 GetDecisionCount(EDecisionType Type = EDecisionType::Movement) const;

    UFUNCTION(BlueprintCallable, Category = "Performance")
    void UpdatePerformanceMetrics();

    // File Operations
    UFUNCTION(BlueprintCallable, Category = "File")
    bool SaveLogsToJSON(const FString& FileName = TEXT(""));

    UFUNCTION(BlueprintCallable, Category = "File")
    bool LoadLogsFromJSON(const FString& FileName);

    UFUNCTION(BlueprintCallable, Category = "File")
    bool ExportLogsToCSV(const FString& FileName = TEXT(""));

    UFUNCTION(BlueprintCallable, Category = "File")
    FString GetLogFilePath() const;

    // Configuration
    UFUNCTION(BlueprintCallable, Category = "Configuration")
    void SetAgentID(const FString& NewAgentID);

    UFUNCTION(BlueprintCallable, Category = "Configuration")
    void SetMatchID(const FString& NewMatchID);

    UFUNCTION(BlueprintCallable, Category = "Configuration")
    void SetLoggingEnabled(bool bEnabled);

    UFUNCTION(BlueprintCallable, Category = "Configuration")
    void AddDecisionTypeFilter(EDecisionType Type);

    UFUNCTION(BlueprintCallable, Category = "Configuration")
    void RemoveDecisionTypeFilter(EDecisionType Type);

    UFUNCTION(BlueprintCallable, Category = "Configuration")
    void ClearDecisionTypeFilters();

    // Utility Functions
    UFUNCTION(BlueprintCallable, Category = "Utility")
    bool IsDecisionTypeFiltered(EDecisionType Type) const;

    UFUNCTION(BlueprintCallable, Category = "Utility")
    FString GenerateUniqueLogFileName() const;

    UFUNCTION(BlueprintCallable, Category = "Utility")
    void PrintLogSummary() const;

protected:
    // Internal Methods
    void InitializeLogger();
    void CreateLogEntry(const FString& DecisionName, const FVector& Location, 
                       EDecisionType Type, const FString& Context);
    void AddLogEntry(const FDecisionLogEntry& Entry);
    bool ShouldLogEntry(const FDecisionLogEntry& Entry) const;
    void ManageMemory();
    void WriteToFile(const FString& Content);
    void UpdateMetricsFromEntry(const FDecisionLogEntry& Entry);

    // File System
    FString GetFullLogPath() const;
    bool EnsureLogDirectoryExists() const;
    FString FormatLogEntryAsJSON(const FDecisionLogEntry& Entry) const;
    FString FormatLogEntryAsCSV(const FDecisionLogEntry& Entry) const;

    // Performance Optimization
    void BatchProcessLogs();
    void CompressOldLogs();

private:
    // Internal State
    TMap<FString, float> PendingDecisionStartTimes;
    float LastFlushTime = 0.0f;
    float LastMetricsUpdate = 0.0f;
    int32 LogEntriesSinceLastFlush = 0;
    
    // File handling
    FString CurrentLogFilePath;
    bool bLogFileInitialized = false;
    
    // Utility functions
    float GetCurrentGameTime() const;
    FString GetTimestampString() const;
    void InitializeLogFile();
    void WriteLogHeader();
};
