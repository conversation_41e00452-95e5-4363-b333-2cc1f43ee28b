# BTTask_SuppressFire - Complete Blueprint Implementation

## 🎯 Blueprint Class Setup

### Class Configuration
- **Parent Class**: `BTTaskNode` (or `BTTask_BlueprintBase`)
- **Blueprint Name**: `BTTask_SuppressFire_BP`
- **Location**: `/Content/AI/BehaviorTree/Tasks/`
- **Category**: "SquadMate AI Tasks"

## 📋 Blueprint Variables

### 🔑 Blackboard Key Selectors
```cpp
// Input Pins (Category: Blackboard Keys)
TargetActor (Blackboard Key Selector)
├── Key Type: Object (Actor)
├── Default: "TargetActor"
└── Tooltip: "Primary target to suppress"

LastKnownEnemyLocation (Blackboard Key Selector)
├── Key Type: Vector
├── Default: "LastKnownEnemyLocation"
└── Tooltip: "Area to suppress if no direct target"

AmmoCount (Blackboard Key Selector)
├── Key Type: Int
├── Default: "AmmoCount"
└── Tooltip: "Current ammunition available"

IsUnderFire (Blackboard Key Selector)
├── Key Type: Bool
├── Default: "IsUnderFire"
└── Tooltip: "Whether agent is under attack"
```

### 🔥 Suppression Settings
```cpp
// Suppression (Category: Suppression Configuration)
SuppressionMode (Enum: ESuppressionMode) = AreaSuppression
├── Values: AreaSuppression, TargetSuppression, CoveringSuppression, RetreatSuppression
└── Tooltip: "Type of suppressive fire to use"

SuppressionDuration (Float) = 5.0
├── Meta: (ClampMin="1.0", ClampMax="30.0")
└── Tooltip: "How long to maintain suppression"

SuppressionRange (Float) = 1000.0
├── Meta: (ClampMin="300.0", ClampMax="2000.0")
└── Tooltip: "Maximum effective suppression range"

SuppressionSpread (Float) = 200.0
├── Meta: (ClampMin="50.0", ClampMax="500.0")
└── Tooltip: "Area spread for suppression pattern"

AmmoLimit (Integer) = 30
├── Meta: (ClampMin="5", ClampMax="100")
└── Tooltip: "Maximum ammo to use for suppression"
```

### 🎯 Fire Pattern Settings
```cpp
// Fire Pattern (Category: Fire Pattern)
FirePattern (Enum: ESuppressionPattern) = BurstPattern
├── Values: Continuous, BurstPattern, RandomPattern, PulsePattern
└── Tooltip: "Firing pattern to use"

BurstDuration (Float) = 1.0
├── Meta: (ClampMin="0.5", ClampMax="3.0")
└── Tooltip: "Duration of each burst"

BurstCooldown (Float) = 0.5
├── Meta: (ClampMin="0.1", ClampMax="2.0")
└── Tooltip: "Pause between bursts"

ShotsPerBurst (Integer) = 5
├── Meta: (ClampMin="2", ClampMax="15")
└── Tooltip: "Number of shots per burst"

FireRate (Float) = 0.1
├── Meta: (ClampMin="0.05", ClampMax="0.5")
└── Tooltip: "Time between individual shots"

SuppressionAccuracy (Float) = 0.3
├── Meta: (ClampMin="0.1", ClampMax="0.8")
└── Tooltip: "Intentionally reduced accuracy for area effect"
```

### 📢 Communication Settings
```cpp
// Communication (Category: Communication)
CallOutSuppression (Boolean) = true
└── Tooltip: "Announce suppression to squad"

SuppressionMessage (String) = "Suppressing enemy position!"
└── Tooltip: "Message to broadcast"

CoveringFireMessage (String) = "Providing covering fire!"
└── Tooltip: "Message for covering fire mode"
```

## 🔧 Event Graph Implementation

### 📍 Event Receive Execute AI
```cpp
Event Receive Execute AI
├── Sequence: Input Validation
│   ├── Get Blackboard Component
│   ├── Get Controlled Pawn
│   ├── Function Call: Validate Suppression Conditions
│   ├── Branch: Can Suppress?
│   │   ├── False → Finish Execute (Failed)
│   │   └── True → Continue
│   └── Function Call: Check Ammo Availability
├── Custom Event: Initialize Suppression
└── Return Value: In Progress
```

### 🔥 Initialize Suppression
```cpp
Custom Event: Initialize Suppression
├── Sequence: Setup Suppression State
│   ├── Set Variable: SuppressionStartTime (Current Time)
│   ├── Set Variable: ShotsFired (0)
│   ├── Set Variable: AmmoAtStart (Current Ammo)
│   ├── Set Variable: IsCurrentlySuppressing (True)
│   └── Function Call: Determine Suppression Target
├── Sequence: Configure Fire Pattern
│   ├── Switch on Enum (FirePattern)
│   │   ├── Continuous → Custom Event: Setup Continuous Fire
│   │   ├── BurstPattern → Custom Event: Setup Burst Pattern
│   │   ├── RandomPattern → Custom Event: Setup Random Pattern
│   │   └── PulsePattern → Custom Event: Setup Pulse Pattern
│   └── Function Call: Generate Suppression Points
├── Sequence: Position and Stance
│   ├── Function Call: Optimize Firing Position
│   ├── Function Call: Set Optimal Stance
│   │   ├── Branch: Has Cover?
│   │   │   ├── True → Set Stance: Crouching
│   │   │   └── False → Set Stance: Standing
│   │   └── Update Animation State
│   └── Function Call: Orient Toward Target Area
├── Function Call: Broadcast Suppression Start
└── Custom Event: Begin Fire Execution
```

### 💥 Begin Fire Execution
```cpp
Custom Event: Begin Fire Execution
├── Sequence: Start Fire Timer
│   ├── Set Timer by Function Name
│   │   ├── Function Name: "Execute Suppression Shot"
│   │   ├── Time: FireRate
│   │   ├── Looping: True
│   │   └── Store Handle: SuppressionFireTimer
│   ├── Set Timer by Function Name
│   │   ├── Function Name: "Monitor Suppression Progress"
│   │   ├── Time: 0.2
│   │   ├── Looping: True
│   │   └── Store Handle: SuppressionMonitorTimer
│   └── Set Timer by Function Name
│       ├── Function Name: "Complete Suppression"
│       ├── Time: SuppressionDuration
│       ├── Looping: False
│       └── Store Handle: SuppressionCompleteTimer
├── Function Call: Play Suppression Animation
└── Function Call: Start Suppression Effects
```

### 🎯 Execute Suppression Shot
```cpp
Function: Execute Suppression Shot
├── Sequence: Pre-Shot Validation
│   ├── Function Call: Check Ammo Remaining
│   ├── Branch: Has Ammo?
│   │   ├── False → Custom Event: Handle Ammo Depleted
│   │   └── True → Continue
│   ├── Function Call: Check Suppression Limits
│   ├── Branch: Within Limits?
│   │   ├── False → Custom Event: Complete Suppression
│   │   └── True → Continue
│   └── Function Call: Validate Target Area
├── Sequence: Calculate Shot
│   ├── Function Call: Get Next Suppression Point
│   ├── Function Call: Apply Suppression Spread
│   │   ├── Get Base Aim Point
│   │   ├── Generate Random Offset
│   │   │   ├── Random Float in Range (-SuppressionSpread, SuppressionSpread)
│   │   │   ├── Convert to 3D Offset
│   │   │   └── Apply to Aim Point
│   │   └── Ensure Point is Valid
│   └── Function Call: Apply Accuracy Reduction
├── Sequence: Execute Shot
│   ├── Function Call: Fire Weapon at Point
│   │   ├── Line Trace to Suppression Point
│   │   ├── Create Bullet Trail Effect
│   │   ├── Play Muzzle Flash
│   │   └── Play Fire Sound
│   ├── Function Call: Apply Recoil
│   ├── Add to Variable: ShotsFired (+1)
│   ├── Subtract from Blackboard: AmmoCount (-1)
│   └── Function Call: Update Suppression Pattern
└── Function Call: Log Suppression Shot
```

### 🔄 Setup Burst Pattern
```cpp
Custom Event: Setup Burst Pattern
├── Sequence: Burst Configuration
│   ├── Set Variable: CurrentBurstShots (0)
│   ├── Set Variable: InBurstMode (True)
│   ├── Set Variable: BurstStartTime (Current Time)
│   └── Set Variable: NextBurstTime (0.0)
├── Function Call: Calculate Burst Timing
│   ├── Shots in Burst: ShotsPerBurst
│   ├── Burst Duration: BurstDuration
│   ├── Shot Interval: BurstDuration / ShotsPerBurst
│   └── Store Calculated Values
└── Function Call: Log Burst Pattern Setup
```

### ⏱️ Monitor Suppression Progress
```cpp
Function: Monitor Suppression Progress
├── Sequence: Progress Tracking
│   ├── Get Current Time
│   ├── Calculate Elapsed Time
│   ├── Calculate Progress Percentage
│   └── Update Progress Variables
├── Sequence: Pattern Management
│   ├── Switch on Enum (FirePattern)
│   │   ├── BurstPattern → Function Call: Manage Burst Timing
│   │   │   ├── Check if Burst Complete
│   │   │   ├── Branch: Burst Finished?
│   │   │   │   ├── True → Start Burst Cooldown
│   │   │   │   └── False → Continue Burst
│   │   │   └── Update Burst State
│   │   ├── RandomPattern → Function Call: Manage Random Timing
│   │   └── PulsePattern → Function Call: Manage Pulse Timing
│   └── Function Call: Update Fire Rate
├── Sequence: Effectiveness Monitoring
│   ├── Function Call: Evaluate Suppression Effectiveness
│   ├── Function Call: Check Target Response
│   ├── Function Call: Adjust Suppression Parameters
│   └── Function Call: Update Squad on Progress
└── Branch: Should Continue?
    ├── Check Ammo Remaining
    ├── Check Time Remaining
    ├── Check Target Status
    ├── False → Custom Event: Complete Suppression
    └── True → Continue
```

## 🔧 Core Functions

### 🎯 Determine Suppression Target
```cpp
Function: Determine Suppression Target
├── Output: Target Location (Vector)
├── Implementation:
│   ├── Get Blackboard Value (TargetActor)
│   ├── Branch: Has Direct Target?
│   │   ├── True → Use Target Actor Location
│   │   └── False → Use Last Known Enemy Location
│   ├── Switch on Enum (SuppressionMode)
│   │   ├── AreaSuppression → Expand Target Area
│   │   │   ├── Create Suppression Zone
│   │   │   ├── Radius: SuppressionSpread
│   │   │   └── Generate Multiple Points
│   │   ├── TargetSuppression → Focus on Specific Target
│   │   ├── CoveringSuppression → Cover Ally Movement
│   │   │   ├── Get Squad Member Positions
│   │   │   ├── Calculate Movement Paths
│   │   │   └── Suppress Threat Areas
│   │   └── RetreatSuppression → Cover Own Retreat
│   │       ├── Calculate Retreat Path
│   │       ├── Identify Threat Angles
│   │       └── Suppress Pursuit Routes
│   └── Validate Target Location
│       ├── Check Range
│       ├── Check Line of Sight
│       └── Ensure Valid Coordinates
```

### 🎯 Generate Suppression Points
```cpp
Function: Generate Suppression Points
├── Input: Center Location (Vector), Spread Radius (Float)
├── Output: Suppression Points (Array of Vectors)
├── Implementation:
│   ├── Clear Existing Points Array
│   ├── Calculate Number of Points
│   │   └── Based on Suppression Mode and Area Size
│   ├── For Loop: Generate Points
│   │   ├── Switch on Pattern Type
│   │   │   ├── Grid Pattern → Create Grid Layout
│   │   │   ├── Circle Pattern → Create Circular Layout
│   │   │   ├── Line Pattern → Create Linear Sweep
│   │   │   └── Random Pattern → Generate Random Points
│   │   ├── Validate Each Point
│   │   │   ├── Check if Reachable by Bullets
│   │   │   ├── Ensure Within Range
│   │   │   └── Avoid Friendly Fire Zones
│   │   ├── Add Valid Points to Array
│   │   └── Continue Loop
│   ├── Sort Points by Priority
│   │   ├── Distance to Target
│   │   ├── Tactical Value
│   │   └── Safety Considerations
│   └── Return Suppression Points Array
```

### 🔥 Fire Weapon at Point
```cpp
Function: Fire Weapon at Point
├── Input: Target Point (Vector)
├── Implementation:
│   ├── Get Weapon Muzzle Location
│   ├── Calculate Fire Direction
│   │   └── Normalize (Target Point - Muzzle Location)
│   ├── Line Trace by Channel
│   │   ├── Start: Muzzle Location
│   │   ├── End: Target Point
│   │   ├── Channel: Visibility
│   │   └── Ignore: Self + Squad Members
│   ├── Branch: Hit Something?
│   │   ├── True → Function Call: Create Impact Effect
│   │   │   ├── Spawn Impact Particles
│   │   │   ├── Create Impact Decal
│   │   │   ├── Play Impact Sound
│   │   │   └── Apply Suppression Effect to Nearby Enemies
│   │   └── False → Function Call: Create Miss Effect
│   ├── Function Call: Create Bullet Trail
│   │   ├── Start: Muzzle Location
│   │   ├── End: Hit Location or Target Point
│   │   ├── Trail Effect: Based on Weapon Type
│   │   └── Duration: 0.5 seconds
│   ├── Function Call: Play Muzzle Effects
│   │   ├── Muzzle Flash Particle
│   │   ├── Fire Sound
│   │   └── Shell Eject Effect
│   └── Function Call: Apply Weapon Recoil
```

### 📊 Evaluate Suppression Effectiveness
```cpp
Function: Evaluate Suppression Effectiveness
├── Output: Effectiveness Score (Float 0.0-1.0)
├── Implementation:
│   ├── Variable: Total Score (Float) = 0.0
│   ├── Sequence: Enemy Response Analysis
│   │   ├── Multi Sphere Trace (Suppression Area)
│   │   ├── Count Enemies in Area
│   │   ├── Check Enemy Movement Patterns
│   │   │   ├── Are Enemies Taking Cover?
│   │   │   ├── Are Enemies Retreating?
│   │   │   └── Are Enemies Suppressed?
│   │   ├── Calculate Response Score
│   │   └── Add to Total Score
│   ├── Sequence: Area Denial Analysis
│   │   ├── Check if Area is Clear
│   │   ├── Monitor Enemy Advance
│   │   ├── Evaluate Movement Restriction
│   │   └── Add Area Denial Score
│   ├── Sequence: Squad Support Analysis
│   │   ├── Check if Allies Can Advance
│   │   ├── Monitor Ally Safety
│   │   ├── Evaluate Mission Progress
│   │   └── Add Support Score
│   ├── Sequence: Resource Efficiency
│   │   ├── Calculate Ammo Usage Rate
│   │   ├── Compare to Standard Efficiency
│   │   ├── Factor in Suppression Duration
│   │   └── Add Efficiency Score
│   └── Return: Normalized Total Score
```

## 🎮 Event Receive Tick AI
```cpp
Event Receive Tick AI
├── Branch: Is Currently Suppressing?
│   ├── Get Variable: IsCurrentlySuppressing
│   ├── False → Return (Skip Tick)
│   └── True → Continue
├── Sequence: Continuous Monitoring
│   ├── Function Call: Monitor Target Area
│   │   ├── Check for New Threats
│   │   ├── Update Target Priorities
│   │   └── Adjust Suppression Focus
│   ├── Function Call: Monitor Ammo Status
│   │   ├── Check Remaining Ammo
│   │   ├── Calculate Usage Rate
│   │   └── Predict Depletion Time
│   ├── Function Call: Monitor Squad Status
│   │   ├── Check Squad Member Positions
│   │   ├── Monitor Squad Communications
│   │   └── Adjust Support Priorities
│   └── Function Call: Update Suppression Pattern
│       ├── Adapt to Enemy Behavior
│       ├── Optimize Fire Distribution
│       └── Maintain Suppression Pressure
└── Branch: Debug Enabled?
    ├── True → Function Call: Update Debug Display
    │   ├── Draw Suppression Area
    │   ├── Show Fire Pattern
    │   ├── Display Effectiveness Metrics
    │   └── Show Ammo Status
    └── False → (No Action)
```

## 📊 Input/Output Pin Configuration

### 🔌 Input Execution Pins
- **Execute**: Main execution entry point
- **Abort**: Emergency stop suppression

### 🔌 Output Execution Pins
- **Success**: Suppression completed successfully
- **Failed**: Unable to suppress effectively
- **In Progress**: Currently suppressing target area

### 📋 Input Data Pins
- **Owner Controller**: AI Controller reference
- **Controlled Pawn**: Character performing suppression
- **Target Area**: Area to suppress (optional override)
- **Duration Override**: Custom suppression duration

### 📋 Output Data Pins
- **Shots Fired**: Total rounds expended
- **Effectiveness Score**: Suppression effectiveness rating
- **Suppression Time**: Actual duration of suppression
- **Ammo Remaining**: Ammunition left after suppression

This Blueprint implementation provides a sophisticated suppressive fire system with multiple patterns, effectiveness monitoring, and intelligent resource management!
