# Enhanced SquadMate AI - 5v5 TDM Deployment Guide

This comprehensive guide covers the complete setup and deployment of the enhanced SquadMate AI system optimized for PUBG-style 5v5 Team Deathmatch scenarios.

## 🎯 System Overview

The enhanced system provides:
- **Advanced Behavior Tree** with 8 tactical states
- **Dynamic JSON configuration** for real-time tuning
- **Role-based AI** with 5 specialized roles
- **EQS-powered positioning** for cover and flanking
- **Comprehensive logging** and performance analytics
- **Blueprint integration** for visual scripting

## 📋 Prerequisites

### Engine and Tools
- **Unreal Engine 5.1+** with AI modules enabled
- **Visual Studio 2022** for C++ compilation
- **JSON parsing** capabilities enabled
- **Behavior Tree Editor** plugin active
- **Environmental Query System** plugin active

### Hardware Requirements
- **CPU**: Intel i7-9700K / AMD Ryzen 7 3700X or better
- **RAM**: 32GB recommended for 5v5 matches
- **GPU**: RTX 3070 / RX 6700 XT or better
- **Storage**: 15GB free space for assets and logs

## 🚀 Installation Steps

### 1. Core System Setup

#### Copy Enhanced Source Files
```
Source/SquadMateAI/
├── Public/
│   ├── AI/
│   │   ├── SquadMateAIController.h (Enhanced)
│   │   ├── SquadMateCharacter.h
│   │   └── SquadManager.h
│   ├── Components/
│   │   ├── HealthComponent.h
│   │   ├── InventoryComponent.h
│   │   ├── ReviveComponent.h
│   │   └── DecisionLoggerComponent.h
│   ├── BehaviorTree/
│   │   ├── Tasks/
│   │   │   ├── BTTask_ReviveAlly.h
│   │   │   ├── BTTask_SuppressiveFire.h
│   │   │   ├── BTTask_EQS_FindFlankPosition.h
│   │   │   ├── BTTask_FindCover.h
│   │   │   └── BTTask_EngageEnemy.h
│   │   └── Services/
│   │       └── BTService_UpdateCombatState.h
│   └── UI/
│       └── SquadHUD.h
└── Private/
    └── [Implementation files]
```

#### Update Build Configuration
```cpp
// SquadMateAI.Build.cs - Enhanced dependencies
PublicDependencyModuleNames.AddRange(new string[] 
{ 
    "Core", "CoreUObject", "Engine", "AIModule",
    "GameplayTasks", "NavigationSystem", "UMG",
    "Json", "JsonUtilities", "HTTP"
});

PrivateDependencyModuleNames.AddRange(new string[] 
{
    "GameplayTags", "RenderCore", "RHI",
    "EnvironmentalQuerySystem", "AITestSuite"
});
```

### 2. Enhanced Blackboard Setup

#### Create BB_SquadMate_Enhanced
Add the following keys to your blackboard:

| Key Name | Type | Default | Priority |
|----------|------|---------|----------|
| TargetActor | Object (Actor) | None | Critical |
| HasLineOfSight | Bool | False | Critical |
| IsUnderFire | Bool | False | Critical |
| CoverLocation | Vector | (0,0,0) | High |
| SquadLeader | Object (Actor) | None | High |
| ReviveTarget | Object (Actor) | None | High |
| TacticState | Enum (Int) | 0 (Patrol) | High |
| PreferredZone | Vector | (0,0,0) | Medium |
| AmmoLow | Bool | False | Medium |
| IsReviving | Bool | False | Medium |
| RoleType | String | "Assault" | Medium |
| FlankLocation | Vector | (0,0,0) | Medium |
| CombatIntensity | Enum (Int) | 0 (None) | Low |
| VisibleEnemies | Object Array | Empty | Low |
| NearbyAllies | Object Array | Empty | Low |

### 3. Enhanced Behavior Tree Structure

#### Create BT_SquadMate_Enhanced
Build the behavior tree following this hierarchy:

```
Root
└── Selector: [Main Decision Hub]
    ├── Sequence: [🔁 REVIVING STATE] (Priority 1)
    │   ├── Decorator: BB (IsReviving == true)
    │   └── Task: BTTask_WaitWhileReviving
    │
    ├── Sequence: [🩹 REVIVE ALLY] (Priority 2)
    │   ├── Decorator: BB (ReviveTarget != null)
    │   ├── Decorator: Cooldown (2.0s)
    │   ├── Task: MoveTo (ReviveTarget)
    │   └── Task: BTTask_ReviveAlly
    │
    ├── Sequence: [🚨 UNDER FIRE EMERGENCY] (Priority 3)
    │   ├── Decorator: BB (IsUnderFire == true)
    │   ├── Decorator: Cooldown (0.5s)
    │   ├── Parallel: [Emergency Response]
    │   │   ├── Task: BTTask_FindCover (EQS)
    │   │   └── Task: BTTask_SuppressiveFire
    │   └── Task: MoveTo (CoverLocation)
    │
    ├── Sequence: [🔫 DIRECT ENGAGEMENT] (Priority 4)
    │   ├── Decorator: BB (TargetActor != null)
    │   ├── Decorator: BB (HasLineOfSight == true)
    │   ├── Service: BTService_UpdateCombatState (0.1s)
    │   └── Task: BTTask_EngageEnemy
    │
    ├── Sequence: [🎯 FLANKING MANEUVER] (Priority 5)
    │   ├── Decorator: BB (TargetActor != null)
    │   ├── Decorator: BB (TacticState == Flank)
    │   ├── Task: BTTask_EQS_FindFlankPosition
    │   └── Task: MoveTo (FlankLocation)
    │
    └── Sequence: [🚶 PATROL/HOLD] (Priority 6)
        ├── Service: BTService_AreaScan (1.0s)
        └── Task: BTTask_PatrolZone
```

### 4. EQS Query Configuration

#### EQS_FindCover_Enhanced
```cpp
Generator: Grid 25x25
├── Space Between: 80 units
├── Generation Radius: 1200 units
└── Height Offset: 0 units

Tests:
├── Distance to Enemy (Weight: 0.3)
│   ├── Min: 200 units
│   └── Max: 1000 units
├── Cover Quality (Weight: 0.4)
│   ├── Height Check: 120+ units
│   └── Material Density
├── Line of Sight (Weight: 0.2)
│   └── Partial visibility preferred
├── Pathfinding (Weight: 0.25)
│   └── Navigation mesh required
└── Ally Distance (Weight: 0.15)
    ├── Min: 100 units
    └── Max: 400 units
```

#### EQS_FlankRoute_Enhanced
```cpp
Generator: Points on Circle
├── Circle Radius: 600-1000 units
├── Points Count: 24
└── Arc Angle: 270 degrees

Tests:
├── Flank Angle (Weight: 0.5)
│   ├── Optimal: 90 degrees
│   └── Tolerance: 45 degrees
├── Cover Availability (Weight: 0.3)
├── Stealth Factor (Weight: 0.2)
├── Movement Time (Weight: 0.15)
└── Coordination Score (Weight: 0.1)
```

### 5. JSON Configuration Integration

#### Load SquadTacticDecision.json
```cpp
// In SquadMateAIController::BeginPlay()
void ASquadMateAIController::LoadTacticalConfiguration()
{
    FString ConfigPath = FPaths::ProjectContentDir() + 
                        TEXT("AI/Config/SquadTacticDecision.json");
    
    FString JsonString;
    if (FFileHelper::LoadFileToString(JsonString, *ConfigPath))
    {
        TSharedPtr<FJsonObject> JsonObject;
        TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);
        
        if (FJsonSerializer::Deserialize(Reader, JsonObject))
        {
            ApplyTacticalConfiguration(JsonObject);
        }
    }
}
```

#### Runtime Configuration Updates
```cpp
// Console command for live tuning
UFUNCTION(Exec)
void ReloadAIConfig()
{
    for (auto& Controller : GetWorld()->GetControllerIterator())
    {
        if (ASquadMateAIController* AIController = 
            Cast<ASquadMateAIController>(Controller.Get()))
        {
            AIController->LoadTacticalConfiguration();
        }
    }
}
```

### 6. 5v5 Match Setup

#### Game Mode Configuration
```cpp
// BP_SquadMate_GameMode
Match Settings:
├── Team Size: 5 players each
├── Match Duration: 10 minutes
├── Respawn: Disabled
├── Revive: Enabled (3 second timer)
├── Victory: Last team standing
└── Map: Urban/Industrial environment

AI Spawn Configuration:
├── Team 1 (Blue): Spawn points A1-A5
├── Team 2 (Red): Spawn points B1-B5
├── Role Distribution:
│   ├── Support: 1 per team
│   ├── Assault: 2 per team
│   ├── Scout: 1 per team
│   ├── Anchor: 1 per team
│   └── Sniper: 0 (optional)
└── Equipment Loadouts: Role-specific
```

#### Level Requirements
```cpp
Map Features Required:
├── Navigation Mesh: Complete coverage
├── Cover Objects: 50+ tactical positions
├── Elevation Changes: Multi-level combat
├── Flanking Routes: Multiple approach paths
├── Choke Points: 3-4 strategic bottlenecks
├── Open Areas: Long-range engagement zones
└── Close Quarters: CQB environments

Lighting and Visibility:
├── Clear sight lines: 1000+ units
├── Shadow areas: Stealth opportunities
├── Dynamic lighting: Optional
└── Weather effects: Optional
```

### 7. Performance Optimization

#### LOD System Implementation
```cpp
AI Update Frequencies by Distance:
├── 0-500m: Full AI (60 FPS)
├── 500-1000m: Reduced AI (30 FPS)
├── 1000-1500m: Basic AI (15 FPS)
└── 1500m+: Minimal AI (5 FPS)

EQS Optimization:
├── Query Caching: 5 second duration
├── Result Pooling: Reuse valid results
├── Distance Culling: Skip distant queries
└── Async Processing: Non-blocking execution
```

#### Memory Management
```cpp
Memory Optimization:
├── Decision Log Rotation: 1000 entries max
├── Blackboard Cleanup: Remove stale data
├── Component Pooling: Reuse objects
├── Texture Streaming: LOD-based loading
└── Audio Culling: Distance-based cleanup
```

### 8. Testing and Validation

#### Automated Testing Suite
```cpp
Test Scenarios:
├── 1v1 Engagement Test
├── 2v2 Squad Coordination Test
├── 5v5 Full Match Test
├── Role Behavior Validation
├── Performance Stress Test
└── Configuration Loading Test

Success Criteria:
├── 60 FPS with 10 AI agents
├── <100ms decision response time
├── 90%+ successful revives
├── Proper role behavior execution
├── Stable memory usage
└── No critical bugs/crashes
```

#### Debug Tools and Commands
```cpp
Console Commands:
├── ai.DebugSquadMate [AgentName] - Show AI debug
├── ai.ReloadConfig - Reload JSON config
├── ai.ShowDecisions - Display decision log
├── ai.ToggleHUD - Show/hide AI HUD
├── ai.SetRole [Agent] [Role] - Change role
├── ai.ForceState [Agent] [State] - Force tactic
└── ai.PerformanceStats - Show performance data

Debug Visualization:
├── EQS Query Results
├── Behavior Tree State
├── Blackboard Values
├── Decision History
├── Performance Metrics
└── Squad Formation
```

### 9. Deployment Checklist

#### Pre-Deployment Validation
- [ ] All C++ code compiles without warnings
- [ ] Enhanced Blackboard configured correctly
- [ ] Behavior Tree logic validated
- [ ] EQS queries return optimal results
- [ ] JSON configuration loads successfully
- [ ] All 5 roles behave distinctly
- [ ] Squad coordination functional
- [ ] UI elements display correctly
- [ ] Performance targets met
- [ ] No memory leaks detected

#### Production Deployment
- [ ] Package project for target platform
- [ ] Include all AI assets and configurations
- [ ] Test on target hardware
- [ ] Validate network synchronization (if MP)
- [ ] Deploy logging and analytics
- [ ] Monitor performance metrics
- [ ] Prepare hotfix deployment system

### 10. Post-Deployment Monitoring

#### Analytics and Metrics
```cpp
Key Performance Indicators:
├── Average match duration
├── Kill/Death ratios per role
├── Revive success rates
├── Flanking maneuver effectiveness
├── Cover usage statistics
├── Decision accuracy scores
├── Player satisfaction ratings
└── System performance metrics
```

#### Continuous Improvement
```cpp
Optimization Cycle:
├── Collect match data
├── Analyze AI behavior patterns
├── Identify improvement areas
├── Update JSON configurations
├── Test changes in controlled environment
├── Deploy incremental updates
└── Monitor impact on gameplay
```

This enhanced deployment guide provides a complete roadmap for implementing a production-ready PUBG-style AI teammate system that delivers tactical, coordinated, and engaging 5v5 Team Deathmatch gameplay.
