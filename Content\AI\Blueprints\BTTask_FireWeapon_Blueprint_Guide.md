# BTTask_FireWeapon Blueprint Implementation Guide

This guide shows how to create Blueprint versions of the core Behavior Tree tasks for the SquadMate AI system.

## 🎯 BTTask_FireWeapon Blueprint Setup

### Blueprint Class Creation
1. **Create New Blueprint Class**
   - Parent Class: `BTTaskNode` (or `BTTask_BlueprintBase`)
   - Name: `BTTask_FireWeapon_BP`
   - Location: `/Content/AI/BehaviorTree/Tasks/`

### 📋 Blueprint Variables

#### Blackboard Key References
```cpp
// Blackboard Keys (Blackboard Key Selector)
TargetActor (Blackboard Key Selector)
HasLineOfSight (Blackboard Key Selector)
AmmoCount (Blackboard Key Selector)
IsReloading (Blackboard Key Selector)
WeaponType (Blackboard Key Selector)
```

#### Fire Control Variables
```cpp
// Fire Control (Category: Fire Control)
FireRate (Float) = 0.1
BurstSize (Integer) = 3
BurstCooldown (Float) = 0.5
MaxFireDuration (Float) = 5.0
AccuracyModifier (Float) = 0.8
UseFullAuto (Boolean) = false
```

#### Weapon Configuration
```cpp
// Weapon Config (Category: Weapon)
MinEngagementRange (Float) = 50.0
MaxEngagementRange (Float) = 1000.0
OptimalRange (Float) = 400.0
DamagePerShot (Float) = 30.0
RecoilAmount (Float) = 0.1
```

#### Animation and Effects
```cpp
// Animation (Category: Animation)
FireMontage (Animation Montage)
AimOffset (Aim Offset)
UseRootMotion (Boolean) = false
```

#### Debug Options
```cpp
// Debug (Category: Debug)
DrawDebugLines (Boolean) = false
LogFireEvents (Boolean) = true
DebugDuration (Float) = 2.0
```

### 🔧 Event Graph Implementation

#### Event Receive Execute AI
```cpp
Event Receive Execute AI
├── Get Blackboard Component
├── Get Controlled Pawn
├── Validate Inputs
│   ├── Is Valid (Controlled Pawn)
│   ├── Is Valid (Target Actor from Blackboard)
│   └── Check Has Line of Sight
├── Branch: Can Fire Weapon?
│   ├── True: Start Fire Sequence
│   └── False: Finish Execute (Failed)
└── Return Value: In Progress
```

#### Fire Sequence Logic
```cpp
Start Fire Sequence
├── Get Current Weapon Info
├── Calculate Aim Point
│   ├── Get Target Location
│   ├── Apply Prediction
│   └── Add Accuracy Spread
├── Set Timer by Function Name
│   ├── Function Name: "ProcessSingleShot"
│   ├── Time: FireRate
│   ├── Looping: True
└── Set Custom Timer Handle
```

#### Process Single Shot Function
```cpp
Function: ProcessSingleShot
├── Check Ammo Count
│   ├── Branch: Ammo > 0
│   │   ├── True: Continue
│   │   └── False: Start Reload Sequence
├── Check Line of Sight
│   ├── Line Trace by Channel
│   ├── Start: Weapon Muzzle Location
│   ├── End: Target Aim Point
│   └── Channel: Visibility
├── Branch: Has Clear Shot?
│   ├── True: Fire Weapon
│   └── False: Adjust Aim
├── Apply Damage to Target
├── Play Fire Animation
├── Spawn Muzzle Flash
├── Update Ammo Count
├── Apply Recoil
└── Check Fire Completion
```

#### Fire Weapon Implementation
```cpp
Fire Weapon
├── Play Animation Montage (Fire Montage)
├── Spawn Particle System at Location
│   ├── Emitter Template: Muzzle Flash
│   └── Location: Weapon Muzzle
├── Play Sound at Location
│   ├── Sound: Fire Sound
│   └── Location: Weapon Muzzle
├── Apply Damage
│   ├── Damage Amount: DamagePerShot
│   ├── Damage Type: Bullet Damage
│   └── Instigator: Controlled Pawn
└── Create Bullet Trail Effect
```

#### Aim Calculation Function
```cpp
Function: Calculate Aim Point
├── Input: Target Actor
├── Get Target Velocity
├── Calculate Prediction Time
│   └── Distance to Target / Projectile Speed
├── Predict Target Location
│   └── Current Location + (Velocity * Prediction Time)
├── Apply Accuracy Spread
│   ├── Random Float in Range (-AccuracySpread, AccuracySpread)
│   ├── Convert to Vector Offset
│   └── Add to Predicted Location
└── Return: Final Aim Point
```

### 🎮 Animation Integration

#### Animation Blueprint Events
```cpp
// In Character Animation Blueprint
AnimNotify_FireWeapon
├── Get Owning Actor
├── Cast to SquadMate Character
├── Get AI Controller
├── Cast to SquadMate AI Controller
└── Call: TriggerWeaponFire
```

#### Montage Setup
```cpp
Fire Montage Configuration:
├── Slot: UpperBody
├── Blend In Time: 0.1s
├── Blend Out Time: 0.2s
├── Play Rate: 1.0
├── Loop: false
└── Animation Notifies:
    ├── 0.1s: AnimNotify_FireWeapon
    ├── 0.15s: AnimNotify_EjectShell
    └── 0.3s: AnimNotify_FireComplete
```

### 🔄 State Management

#### Task State Variables
```cpp
// Task State (Category: State)
IsCurrentlyFiring (Boolean) = false
ShotsFiredInBurst (Integer) = 0
FireStartTime (Float) = 0.0
LastShotTime (Float) = 0.0
CurrentTarget (Actor Reference)
```

#### State Transitions
```cpp
Event Receive Tick AI
├── Check Task State
├── Branch: Is Currently Firing?
│   ├── True: Update Fire Logic
│   └── False: Check Start Conditions
├── Update Fire Duration
├── Check Completion Conditions
│   ├── Target Lost
│   ├── Out of Ammo
│   ├── Max Duration Reached
│   └── Manual Stop
└── Branch: Should Complete?
    ├── True: Finish Execute (Success/Failed)
    └── False: Continue
```

### 🎯 Accuracy System

#### Accuracy Calculation
```cpp
Function: Calculate Current Accuracy
├── Base Accuracy: AccuracyModifier
├── Distance Modifier
│   ├── Get Distance to Target
│   ├── Compare to Optimal Range
│   └── Apply Distance Penalty
├── Movement Modifier
│   ├── Get Pawn Velocity
│   └── Apply Movement Penalty
├── Stance Modifier
│   ├── Standing: 1.0
│   ├── Crouching: 1.2
│   └── Prone: 1.5
├── Recoil Modifier
│   └── Decrease with Shots Fired
└── Return: Final Accuracy
```

#### Spread Application
```cpp
Function: Apply Accuracy Spread
├── Input: Perfect Aim Point
├── Calculate Spread Radius
│   └── (1.0 - Current Accuracy) * Max Spread
├── Generate Random Offset
│   ├── Random Unit Vector in Cone
│   └── Scale by Spread Radius
├── Add Offset to Aim Point
└── Return: Modified Aim Point
```

### 🔧 Weapon Type Handling

#### Weapon-Specific Behavior
```cpp
Function: Get Weapon Behavior
├── Switch on Weapon Type
│   ├── Assault Rifle:
│   │   ├── Fire Rate: 0.1
│   │   ├── Burst Size: 3
│   │   └── Accuracy: 0.8
│   ├── Sniper Rifle:
│   │   ├── Fire Rate: 1.5
│   │   ├── Burst Size: 1
│   │   └── Accuracy: 0.95
│   ├── SMG:
│   │   ├── Fire Rate: 0.08
│   │   ├── Burst Size: 5
│   │   └── Accuracy: 0.6
│   └── Default:
│       └── Use Base Values
└── Apply Weapon Modifiers
```

### 🎨 Visual Effects

#### Muzzle Flash System
```cpp
Spawn Muzzle Flash
├── Get Weapon Mesh
├── Get Socket Location ("MuzzleFlash")
├── Spawn Particle System
│   ├── Template: Based on Weapon Type
│   ├── Location: Muzzle Socket
│   ├── Rotation: Weapon Forward
│   └── Auto Destroy: True
└── Attach to Weapon Mesh
```

#### Bullet Trail Effect
```cpp
Create Bullet Trail
├── Start Location: Muzzle Position
├── End Location: Hit Location or Max Range
├── Spawn Particle System
│   ├── Template: Bullet Trail
│   ├── Parameter: Start Location
│   ├── Parameter: End Location
│   └── Auto Destroy: True
└── Play Whiz Sound (if near player)
```

### 🔊 Audio Integration

#### Sound Management
```cpp
Play Fire Sound
├── Get Weapon Type
├── Select Appropriate Sound
│   ├── Assault Rifle: AR_Fire_Sound
│   ├── Sniper: Sniper_Fire_Sound
│   └── SMG: SMG_Fire_Sound
├── Play Sound at Location
│   ├── Volume: Based on Distance
│   ├── Pitch: Random Variation
│   └── Attenuation: 3D Spatial
└── Play Shell Eject Sound (Delayed)
```

### 🐛 Debug Visualization

#### Debug Draw System
```cpp
Draw Debug Info (if Debug Enabled)
├── Draw Line (Weapon to Target)
│   ├── Color: Green (Hit) / Red (Miss)
│   ├── Thickness: 2.0
│   └── Duration: DebugDuration
├── Draw Sphere (Target Location)
│   ├── Radius: 50.0
│   ├── Color: Yellow
│   └── Duration: DebugDuration
├── Draw Text (Fire Info)
│   ├── Text: "Firing: [Weapon] at [Target]"
│   ├── Location: Above Character
│   └── Duration: DebugDuration
└── Print String (Console Log)
    └── Text: Fire Event Details
```

### 📊 Performance Optimization

#### Optimization Techniques
```cpp
Performance Optimizations:
├── Cache Frequently Used References
├── Use Object Pooling for Effects
├── Limit Debug Draw Distance
├── Use LOD for Distant Targets
├── Batch Multiple Shots
└── Optimize Trace Channels
```

### 🔗 Integration Points

#### Blackboard Integration
```cpp
Update Blackboard Values:
├── Set AmmoCount (After Each Shot)
├── Set IsReloading (When Ammo Empty)
├── Set LastFireTime (Current Time)
└── Set TargetActor (If Target Lost)
```

#### Component Communication
```cpp
Component Interactions:
├── Inventory Component: Ammo Management
├── Health Component: Damage Application
├── Animation Component: Montage Playback
├── Audio Component: Sound Playback
└── Decision Logger: Event Logging
```

This Blueprint implementation provides a complete, visual scripting solution for weapon firing behavior that integrates seamlessly with the C++ SquadMate AI system.
