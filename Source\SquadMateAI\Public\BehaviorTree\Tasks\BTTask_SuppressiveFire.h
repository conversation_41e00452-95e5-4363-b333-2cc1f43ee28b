#pragma once

#include "CoreMinimal.h"
#include "BehaviorTree/BTTaskNode.h"
#include "AI/SquadMateAIController.h"
#include "BTTask_SuppressiveFire.generated.h"

UENUM(BlueprintType)
enum class ESuppressionMode : uint8
{
    AreaSuppression     UMETA(DisplayName = "Area Suppression"),
    TargetSuppression   UMETA(DisplayName = "Target Suppression"),
    CoveringSuppression UMETA(DisplayName = "Covering Suppression"),
    RetreatSuppression  UMETA(DisplayName = "Retreat Suppression")
};

UENUM(BlueprintType)
enum class ESuppressionPattern : uint8
{
    Continuous      UMETA(DisplayName = "Continuous"),
    BurstPattern    UMETA(DisplayName = "Burst Pattern"),
    RandomPattern   UMETA(DisplayName = "Random Pattern"),
    PulsePattern    UMETA(DisplayName = "Pulse Pattern")
};

/**
 * Behavior Tree task for suppressive fire tactics
 * Provides covering fire to suppress enemies and support team movement
 */
UCLASS(BlueprintType, meta=(DisplayName="Suppressive Fire"))
class SQUADMATEAI_API UBTTask_SuppressiveFire : public UBTTaskNode
{
    GENERATED_BODY()

public:
    UBTTask_SuppressiveFire();

protected:
    virtual EBTNodeResult::Type ExecuteTask(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory) override;
    virtual EBTNodeResult::Type AbortTask(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory) override;
    virtual void TickTask(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, float DeltaSeconds) override;
    virtual void OnTaskFinished(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, EBTNodeResult::Type TaskResult) override;
    virtual FString GetStaticDescription() const override;

    // Blackboard Keys
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    struct FBlackboardKeySelector TargetActorKey;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    struct FBlackboardKeySelector LastKnownEnemyLocationKey;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    struct FBlackboardKeySelector IsUnderFireKey;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    struct FBlackboardKeySelector AmmoCountKey;

    // Suppression Configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Suppression")
    ESuppressionMode SuppressionMode = ESuppressionMode::AreaSuppression;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Suppression")
    ESuppressionPattern FirePattern = ESuppressionPattern::BurstPattern;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Suppression")
    float SuppressionDuration = 5.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Suppression")
    float SuppressionRange = 1200.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Suppression")
    float SuppressionSpread = 200.0f;

    // Fire Pattern Settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fire Pattern")
    float BurstDuration = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fire Pattern")
    float BurstCooldown = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fire Pattern")
    int32 ShotsPerBurst = 5;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fire Pattern")
    float FireRate = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Fire Pattern")
    float RandomFireDelay = 0.2f;

    // Ammo Management
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Ammo")
    int32 MinAmmoToSuppress = 10;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Ammo")
    int32 MaxAmmoToUse = 30;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Ammo")
    float AmmoConservationFactor = 0.7f;

    // Accuracy and Spread
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Accuracy")
    float SuppressionAccuracy = 0.3f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Accuracy")
    float AccuracyDecayRate = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Accuracy")
    float MaxSpreadAngle = 15.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Accuracy")
    bool bUseRandomSpread = true;

    // Stance and Positioning
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stance")
    bool bAutoAdjustStance = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stance")
    bool bPreferCrouchedFire = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stance")
    bool bUseCoverWhileFiring = true;

    // Communication
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Communication")
    bool bCallOutSuppression = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Communication")
    FString SuppressionCallout = TEXT("Suppressing enemy!");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Communication")
    FString CoveringFireCallout = TEXT("Covering fire!");

    // Performance Settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float TargetUpdateFrequency = 0.2f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bUseLineOfSightChecks = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float MaxSuppressionTime = 10.0f;

    // Debug Options
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bDrawDebugInfo = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bLogSuppressionEvents = false;

public:
    // Static utility functions
    UFUNCTION(BlueprintCallable, Category = "Suppression", CallInEditor = true)
    static bool CanSuppressTarget(AActor* Suppressor, const FVector& TargetLocation, float MaxRange = 1200.0f);

    UFUNCTION(BlueprintCallable, Category = "Suppression", CallInEditor = true)
    static float CalculateSuppressionEffectiveness(AActor* Suppressor, const FVector& TargetArea, float Spread = 200.0f);

    UFUNCTION(BlueprintCallable, Category = "Suppression", CallInEditor = true)
    static TArray<FVector> GenerateSuppressionPattern(const FVector& CenterLocation, float Spread, int32 PointCount = 5);

protected:
    // Core suppression logic
    bool InitializeSuppression(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory);
    void UpdateSuppression(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, float DeltaTime);
    void TerminateSuppression(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory);

    // Target selection and tracking
    FVector GetSuppressionTarget(UBehaviorTreeComponent& OwnerComp);
    void UpdateSuppressionTarget(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory);
    bool IsValidSuppressionTarget(const FVector& TargetLocation, AActor* Suppressor);

    // Fire pattern execution
    void ExecuteFirePattern(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, float DeltaTime);
    void ProcessContinuousFire(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, float DeltaTime);
    void ProcessBurstFire(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, float DeltaTime);
    void ProcessRandomFire(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, float DeltaTime);
    void ProcessPulseFire(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, float DeltaTime);

    // Aiming and accuracy
    FVector CalculateSuppressionAimPoint(const FVector& BaseTarget, float Spread);
    FRotator CalculateSuppressionAim(const FVector& FromLocation, const FVector& ToLocation);
    void ApplySuppressionSpread(FVector& AimPoint, float SpreadAmount);
    float GetCurrentAccuracy(uint8* NodeMemory);

    // Ammo and resource management
    bool HasSufficientAmmo(UBehaviorTreeComponent& OwnerComp);
    void ConsumeAmmo(UBehaviorTreeComponent& OwnerComp, int32 Amount = 1);
    bool ShouldConserveAmmo(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory);
    void UpdateAmmoUsage(uint8* NodeMemory, int32 ShotsFired);

    // Stance and positioning
    void OptimizeStanceForSuppression(UBehaviorTreeComponent& OwnerComp, const FVector& TargetLocation);
    bool ShouldUseCover(UBehaviorTreeComponent& OwnerComp, const FVector& TargetLocation);
    void AdjustPositionForSuppression(UBehaviorTreeComponent& OwnerComp);

    // Communication and coordination
    void BroadcastSuppressionStart(UBehaviorTreeComponent& OwnerComp, const FVector& TargetLocation);
    void BroadcastSuppressionEnd(UBehaviorTreeComponent& OwnerComp);
    void RequestAmmoSupport(UBehaviorTreeComponent& OwnerComp);

    // Effectiveness evaluation
    float EvaluateSuppressionEffectiveness(const FVector& TargetLocation, float Duration);
    bool IsSuppressionSuccessful(uint8* NodeMemory);
    void AdjustSuppressionParameters(uint8* NodeMemory, float Effectiveness);

    // Utility functions
    float GetDistanceToTarget(AActor* From, const FVector& To);
    bool HasLineOfSightToTarget(AActor* From, const FVector& To);
    FVector GetWeaponMuzzleLocation(AActor* Actor);

    // Debug and logging
    void LogSuppressionEvent(const FString& Event, UBehaviorTreeComponent& OwnerComp, const FVector& Target = FVector::ZeroVector);
    void DrawDebugSuppressionInfo(UWorld* World, const FVector& SuppressionArea, float Spread, bool bIsActive);

private:
    // Task memory structure
    struct FBTTask_SuppressiveFireMemory
    {
        FVector SuppressionTarget = FVector::ZeroVector;
        float SuppressionStartTime = 0.0f;
        float LastShotTime = 0.0f;
        float LastBurstTime = 0.0f;
        float LastTargetUpdate = 0.0f;
        int32 ShotsFiredInBurst = 0;
        int32 TotalShotsFired = 0;
        int32 AmmoAtStart = 0;
        bool bIsFiring = false;
        bool bInBurstCooldown = false;
        float CurrentAccuracy = 1.0f;
        ESuppressionMode ActiveMode = ESuppressionMode::AreaSuppression;
        TArray<FVector> SuppressionPoints;
        int32 CurrentSuppressionPointIndex = 0;
    };

    // Memory management
    void InitializeTaskMemory(uint8* NodeMemory);
    void CleanupTaskMemory(uint8* NodeMemory);
    FBTTask_SuppressiveFireMemory* GetTaskMemory(uint8* NodeMemory);

    // Fire execution
    void ExecuteSingleShot(UBehaviorTreeComponent& OwnerComp, const FVector& AimPoint);
    bool CanFireWeapon(UBehaviorTreeComponent& OwnerComp);
    void TriggerWeaponFire(AActor* Shooter, const FVector& AimPoint);

    // Pattern generation
    void GenerateSuppressionPoints(uint8* NodeMemory, const FVector& CenterTarget);
    FVector GetNextSuppressionPoint(uint8* NodeMemory);
    void AdvanceSuppressionPattern(uint8* NodeMemory);

    // Suppression mode handlers
    void HandleAreaSuppression(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, float DeltaTime);
    void HandleTargetSuppression(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, float DeltaTime);
    void HandleCoveringSuppression(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, float DeltaTime);
    void HandleRetreatSuppression(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, float DeltaTime);
};
