using UnrealBuildTool;

public class SquadMateAI : ModuleRules
{
    public SquadMateAI(ReadOnlyTargetRules Target) : base(Target)
    {
        PCHUsage = PCHUsageMode.UseExplicitOrSharedPCHs;

        PublicDependencyModuleNames.AddRange(new string[] 
        { 
            "Core", 
            "CoreUObject", 
            "Engine",
            "AIModule",
            "GameplayTasks",
            "NavigationSystem",
            "UMG",
            "Slate",
            "SlateCore"
        });

        PrivateDependencyModuleNames.AddRange(new string[] 
        {
            "GameplayTags",
            "Json",
            "JsonUtilities",
            "HTTP",
            "RenderCore",
            "RHI"
        });

        // AI and Behavior Tree modules
        PublicDependencyModuleNames.AddRange(new string[]
        {
            "AIModule",
            "GameplayTasks",
            "NavigationSystem"
        });

        // EQS (Environmental Query System) support
        if (Target.bBuildEditor)
        {
            PrivateDependencyModuleNames.AddRange(new string[]
            {
                "UnrealEd",
                "ToolMenus",
                "EditorStyle",
                "EditorWidgets",
                "GraphEditor",
                "Kismet",
                "KismetCompiler",
                "PropertyEditor",
                "BlueprintGraph",
                "AIGraph"
            });
        }

        // Platform specific modules
        if (Target.Platform == UnrealTargetPlatform.Win64)
        {
            PrivateDependencyModuleNames.AddRange(new string[]
            {
                "XAudio2"
            });
        }

        // Optimization settings
        OptimizeCode = CodeOptimization.InShippingBuildsOnly;
        
        // Enable RTTI for better debugging
        bUseRTTI = true;
        
        // Enable exceptions for JSON parsing
        bEnableExceptions = true;

        // Define preprocessor macros
        PublicDefinitions.AddRange(new string[]
        {
            "SQUADMATE_AI_ENABLED=1",
            "WITH_DECISION_LOGGING=1"
        });

        if (Target.Configuration == UnrealTargetConfiguration.Debug || 
            Target.Configuration == UnrealTargetConfiguration.DebugGame)
        {
            PublicDefinitions.Add("SQUADMATE_AI_DEBUG=1");
        }
        else
        {
            PublicDefinitions.Add("SQUADMATE_AI_DEBUG=0");
        }

        // Include paths
        PublicIncludePaths.AddRange(new string[]
        {
            "SquadMateAI/Public",
            "SquadMateAI/Public/AI",
            "SquadMateAI/Public/Components",
            "SquadMateAI/Public/BehaviorTree",
            "SquadMateAI/Public/BehaviorTree/Tasks",
            "SquadMateAI/Public/BehaviorTree/Decorators",
            "SquadMateAI/Public/BehaviorTree/Services",
            "SquadMateAI/Public/UI"
        });

        PrivateIncludePaths.AddRange(new string[]
        {
            "SquadMateAI/Private",
            "SquadMateAI/Private/AI",
            "SquadMateAI/Private/Components",
            "SquadMateAI/Private/BehaviorTree",
            "SquadMateAI/Private/UI"
        });
    }
}
