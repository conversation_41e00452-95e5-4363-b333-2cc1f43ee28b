#pragma once

#include "CoreMinimal.h"
#include "AIController.h"
#include "BehaviorTree/BehaviorTreeComponent.h"
#include "BehaviorTree/BlackboardComponent.h"
#include "Perception/AIPerceptionComponent.h"
#include "Components/DecisionLoggerComponent.h"
#include "GameplayTagContainer.h"
#include "Engine/DataTable.h"
#include "SquadMateAIController_Enhanced.generated.h"

class ASquadManager;
class USquadRoleComponent;
class UHealthComponent;
class UInventoryComponent;

DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnTacticStateChanged, ETacticState, OldState, ETacticState, NewState);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnEnemyDetected, AActor*, Enemy);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnSquadRoleChanged, ESquadRole, NewRole);

USTRUCT(BlueprintType)
struct FTacticalConfiguration
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    TMap<ESquadRole, float> RolePriorities;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    TMap<ETacticState, float> TacticWeights;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float DecisionUpdateFrequency = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float CombatIntensityThreshold = 0.7f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bEnableDynamicRoleSwapping = true;

    FTacticalConfiguration()
    {
        DecisionUpdateFrequency = 0.1f;
        CombatIntensityThreshold = 0.7f;
        bEnableDynamicRoleSwapping = true;
    }
};

USTRUCT(BlueprintType)
struct FPerformanceMetrics
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly)
    int32 KillCount = 0;

    UPROPERTY(BlueprintReadOnly)
    int32 DeathCount = 0;

    UPROPERTY(BlueprintReadOnly)
    int32 ReviveCount = 0;

    UPROPERTY(BlueprintReadOnly)
    float AccuracyPercentage = 0.0f;

    UPROPERTY(BlueprintReadOnly)
    float SurvivalTime = 0.0f;

    UPROPERTY(BlueprintReadOnly)
    int32 SuccessfulFlankCount = 0;

    UPROPERTY(BlueprintReadOnly)
    float TeamworkScore = 0.0f;

    FPerformanceMetrics()
    {
        KillCount = 0;
        DeathCount = 0;
        ReviveCount = 0;
        AccuracyPercentage = 0.0f;
        SurvivalTime = 0.0f;
        SuccessfulFlankCount = 0;
        TeamworkScore = 0.0f;
    }
};

/**
 * Enhanced AI Controller for SquadMate characters with JSON configuration loading,
 * advanced decision making, and comprehensive performance tracking
 */
UCLASS(BlueprintType, Blueprintable)
class SQUADMATEAI_API ASquadMateAIController_Enhanced : public AAIController
{
    GENERATED_BODY()

public:
    ASquadMateAIController_Enhanced();

protected:
    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;
    virtual void Tick(float DeltaTime) override;
    virtual void OnPossess(APawn* InPawn) override;
    virtual void OnUnPossess() override;

    // Core AI Components
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AI Components")
    UBehaviorTreeComponent* BehaviorTreeComponent;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AI Components")
    UBlackboardComponent* BlackboardComponent;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AI Components")
    UAIPerceptionComponent* AIPerceptionComponent;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AI Components")
    UDecisionLoggerComponent* DecisionLogger;

    // AI Configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Configuration")
    UBehaviorTree* BehaviorTree;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Configuration")
    UBlackboardAsset* BlackboardAsset;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Configuration")
    FString ConfigurationFilePath = TEXT("AI/Config/SquadTacticDecision.json");

    // Tactical State
    UPROPERTY(BlueprintReadOnly, Category = "Tactical State")
    ETacticState CurrentTacticState = ETacticState::Patrol;

    UPROPERTY(BlueprintReadOnly, Category = "Tactical State")
    ESquadRole CurrentSquadRole = ESquadRole::Assault;

    UPROPERTY(BlueprintReadOnly, Category = "Tactical State")
    float CombatIntensity = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Tactical State")
    bool bIsInCombat = false;

    // Squad Management
    UPROPERTY(BlueprintReadWrite, Category = "Squad")
    ASquadManager* SquadManager = nullptr;

    UPROPERTY(BlueprintReadOnly, Category = "Squad")
    TArray<ASquadMateAIController_Enhanced*> SquadMembers;

    UPROPERTY(BlueprintReadOnly, Category = "Squad")
    ASquadMateAIController_Enhanced* SquadLeader = nullptr;

    // Configuration and Performance
    UPROPERTY(BlueprintReadOnly, Category = "Configuration")
    FTacticalConfiguration TacticalConfig;

    UPROPERTY(BlueprintReadOnly, Category = "Performance")
    FPerformanceMetrics PerformanceMetrics;

    // Events
    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnTacticStateChanged OnTacticStateChanged;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnEnemyDetected OnEnemyDetected;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnSquadRoleChanged OnSquadRoleChanged;

public:
    // Core AI Interface
    UFUNCTION(BlueprintCallable, Category = "AI Control")
    void SetTacticState(ETacticState NewState);

    UFUNCTION(BlueprintCallable, Category = "AI Control")
    ETacticState GetTacticState() const { return CurrentTacticState; }

    UFUNCTION(BlueprintCallable, Category = "AI Control")
    void SetSquadRole(ESquadRole NewRole);

    UFUNCTION(BlueprintCallable, Category = "AI Control")
    ESquadRole GetSquadRole() const { return CurrentSquadRole; }

    UFUNCTION(BlueprintCallable, Category = "AI Control")
    void ForceDecisionUpdate();

    // Configuration Management
    UFUNCTION(BlueprintCallable, Category = "Configuration")
    bool LoadTacticalConfiguration();

    UFUNCTION(BlueprintCallable, Category = "Configuration")
    bool LoadTacticalConfigurationFromFile(const FString& FilePath);

    UFUNCTION(BlueprintCallable, Category = "Configuration")
    void ApplyTacticalConfiguration(const FTacticalConfiguration& NewConfig);

    UFUNCTION(BlueprintCallable, Category = "Configuration")
    FTacticalConfiguration GetTacticalConfiguration() const { return TacticalConfig; }

    // Squad Management
    UFUNCTION(BlueprintCallable, Category = "Squad")
    void RegisterWithSquadManager(ASquadManager* Manager);

    UFUNCTION(BlueprintCallable, Category = "Squad")
    void SetSquadLeader(ASquadMateAIController_Enhanced* Leader);

    UFUNCTION(BlueprintCallable, Category = "Squad")
    void AddSquadMember(ASquadMateAIController_Enhanced* Member);

    UFUNCTION(BlueprintCallable, Category = "Squad")
    void RemoveSquadMember(ASquadMateAIController_Enhanced* Member);

    UFUNCTION(BlueprintCallable, Category = "Squad")
    TArray<ASquadMateAIController_Enhanced*> GetSquadMembers() const { return SquadMembers; }

    // Combat and Perception
    UFUNCTION(BlueprintCallable, Category = "Combat")
    void SetCombatIntensity(float NewIntensity);

    UFUNCTION(BlueprintCallable, Category = "Combat")
    float GetCombatIntensity() const { return CombatIntensity; }

    UFUNCTION(BlueprintCallable, Category = "Combat")
    bool IsInCombat() const { return bIsInCombat; }

    UFUNCTION(BlueprintCallable, Category = "Perception")
    TArray<AActor*> GetVisibleEnemies() const;

    UFUNCTION(BlueprintCallable, Category = "Perception")
    AActor* GetNearestEnemy() const;

    UFUNCTION(BlueprintCallable, Category = "Perception")
    bool HasLineOfSightToActor(AActor* Target) const;

    // Performance Tracking
    UFUNCTION(BlueprintCallable, Category = "Performance")
    void UpdatePerformanceMetrics(const FPerformanceMetrics& NewMetrics);

    UFUNCTION(BlueprintCallable, Category = "Performance")
    FPerformanceMetrics GetPerformanceMetrics() const { return PerformanceMetrics; }

    UFUNCTION(BlueprintCallable, Category = "Performance")
    void IncrementKillCount();

    UFUNCTION(BlueprintCallable, Category = "Performance")
    void IncrementDeathCount();

    UFUNCTION(BlueprintCallable, Category = "Performance")
    void IncrementReviveCount();

    // Blackboard Utilities
    UFUNCTION(BlueprintCallable, Category = "Blackboard")
    void SetBlackboardValueAsBool(const FName& KeyName, bool Value);

    UFUNCTION(BlueprintCallable, Category = "Blackboard")
    void SetBlackboardValueAsFloat(const FName& KeyName, float Value);

    UFUNCTION(BlueprintCallable, Category = "Blackboard")
    void SetBlackboardValueAsInt(const FName& KeyName, int32 Value);

    UFUNCTION(BlueprintCallable, Category = "Blackboard")
    void SetBlackboardValueAsVector(const FName& KeyName, const FVector& Value);

    UFUNCTION(BlueprintCallable, Category = "Blackboard")
    void SetBlackboardValueAsObject(const FName& KeyName, UObject* Value);

    UFUNCTION(BlueprintCallable, Category = "Blackboard")
    bool GetBlackboardValueAsBool(const FName& KeyName) const;

    UFUNCTION(BlueprintCallable, Category = "Blackboard")
    float GetBlackboardValueAsFloat(const FName& KeyName) const;

    UFUNCTION(BlueprintCallable, Category = "Blackboard")
    int32 GetBlackboardValueAsInt(const FName& KeyName) const;

    UFUNCTION(BlueprintCallable, Category = "Blackboard")
    FVector GetBlackboardValueAsVector(const FName& KeyName) const;

    UFUNCTION(BlueprintCallable, Category = "Blackboard")
    UObject* GetBlackboardValueAsObject(const FName& KeyName) const;

    // Decision Making
    UFUNCTION(BlueprintCallable, Category = "Decision Making")
    void EvaluateTacticalSituation();

    UFUNCTION(BlueprintCallable, Category = "Decision Making")
    ETacticState DetermineBestTactic() const;

    UFUNCTION(BlueprintCallable, Category = "Decision Making")
    float CalculateTacticScore(ETacticState Tactic) const;

    // Communication
    UFUNCTION(BlueprintCallable, Category = "Communication")
    void BroadcastToSquad(const FString& Message, float Priority = 1.0f);

    UFUNCTION(BlueprintCallable, Category = "Communication")
    void ReportEnemyContact(AActor* Enemy, const FVector& Location);

    UFUNCTION(BlueprintCallable, Category = "Communication")
    void RequestSupport(const FString& SupportType, const FVector& Location);

    // Console Commands (Development)
    UFUNCTION(Exec)
    void DebugSetTactic(int32 TacticIndex);

    UFUNCTION(Exec)
    void DebugSetRole(int32 RoleIndex);

    UFUNCTION(Exec)
    void DebugReloadConfig();

    UFUNCTION(Exec)
    void DebugShowDecisions();

protected:
    // Internal Methods
    void InitializeAIComponents();
    void InitializePerception();
    void InitializeBlackboard();
    void StartBehaviorTree();

    // Configuration Loading
    bool ParseJSONConfiguration(const FString& JSONString);
    void ApplyJSONConfiguration(TSharedPtr<FJsonObject> ConfigObject);
    void LoadRoleConfiguration(TSharedPtr<FJsonObject> RoleConfig);
    void LoadTacticConfiguration(TSharedPtr<FJsonObject> TacticConfig);

    // Decision Making
    void UpdateDecisionMaking(float DeltaTime);
    void ProcessTacticalTransitions();
    void UpdateCombatState();
    void UpdateSquadCoordination();

    // Perception Callbacks
    UFUNCTION()
    void OnPerceptionUpdated(const TArray<AActor*>& UpdatedActors);

    UFUNCTION()
    void OnTargetPerceptionUpdated(AActor* Actor, FAIStimulus Stimulus);

    // Component Access
    UHealthComponent* GetHealthComponent() const;
    UInventoryComponent* GetInventoryComponent() const;
    USquadRoleComponent* GetSquadRoleComponent() const;

    // Utility Functions
    float CalculateDistanceToActor(AActor* Actor) const;
    bool IsActorEnemy(AActor* Actor) const;
    bool IsActorAlly(AActor* Actor) const;
    FVector GetPawnLocation() const;

private:
    // Internal State
    float LastDecisionUpdate = 0.0f;
    float LastPerformanceUpdate = 0.0f;
    float LastConfigurationLoad = 0.0f;
    
    // Cached References
    TWeakObjectPtr<APawn> CachedPawn;
    TWeakObjectPtr<UHealthComponent> CachedHealthComponent;
    TWeakObjectPtr<UInventoryComponent> CachedInventoryComponent;
    
    // Configuration Cache
    TSharedPtr<FJsonObject> CachedConfigurationJSON;
    
    // Performance Optimization
    const float DecisionUpdateFrequency = 0.1f;
    const float PerformanceUpdateFrequency = 1.0f;
    const float ConfigurationCheckFrequency = 5.0f;
    
    // Blackboard Key Names (Constants)
    static const FName BB_TargetActor;
    static const FName BB_HasLineOfSight;
    static const FName BB_IsUnderFire;
    static const FName BB_CoverLocation;
    static const FName BB_TacticState;
    static const FName BB_SquadRole;
    static const FName BB_AmmoCount;
    static const FName BB_HealthPercentage;
    static const FName BB_IsReviving;
    static const FName BB_ReviveTarget;
    static const FName BB_SquadLeader;
    static const FName BB_FlankLocation;
    static const FName BB_LastKnownEnemyLocation;
    static const FName BB_VisibleEnemies;
    static const FName BB_NearbyAllies;
    static const FName BB_CombatIntensity;
    static const FName BB_PreferredZone;
};
