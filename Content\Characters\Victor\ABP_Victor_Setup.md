# ABP_Victor Animation Blueprint Setup Guide

This document provides a comprehensive guide for setting up the Victor character's Animation Blueprint for the SquadMate AI system.

## Overview

The ABP_Victor Animation Blueprint handles all animation logic for the AI character, including movement, combat, stance changes, and contextual animations.

## Animation Blueprint Structure

### State Machine: Main Locomotion

#### States Overview
1. **Idle/Standing**
2. **Walking/Running**
3. **Crouching**
4. **Prone**
5. **Peek Left/Right**
6. **Combat Stance**
7. **Reloading**
8. **Reviving**
9. **Death**

### Variables

#### Movement Variables
```cpp
// Speed and Direction
UPROPERTY(BlueprintReadOnly, Category = "Movement")
float Speed = 0.0f;

UPROPERTY(BlueprintReadOnly, Category = "Movement")
float Direction = 0.0f;

UPROPERTY(BlueprintReadOnly, Category = "Movement")
FVector Velocity = FVector::ZeroVector;

UPROPERTY(BlueprintReadOnly, Category = "Movement")
bool bIsMoving = false;

UPROPERTY(BlueprintReadOnly, Category = "Movement")
bool bIsInAir = false;

// Movement Type
UPROPERTY(BlueprintReadOnly, Category = "Movement")
bool bIsWalking = false;

UPROPERTY(BlueprintReadOnly, Category = "Movement")
bool bIsRunning = false;

UPROPERTY(BlueprintReadOnly, Category = "Movement")
bool bIsSprinting = false;
```

#### Stance Variables
```cpp
UPROPERTY(BlueprintReadOnly, Category = "Stance")
ECharacterStance CurrentStance = ECharacterStance::Standing;

UPROPERTY(BlueprintReadOnly, Category = "Stance")
bool bIsCrouching = false;

UPROPERTY(BlueprintReadOnly, Category = "Stance")
bool bIsProne = false;

UPROPERTY(BlueprintReadOnly, Category = "Stance")
bool bIsPeeking = false;

UPROPERTY(BlueprintReadOnly, Category = "Stance")
bool bIsPeekingLeft = false;

UPROPERTY(BlueprintReadOnly, Category = "Stance")
bool bIsPeekingRight = false;

UPROPERTY(BlueprintReadOnly, Category = "Stance")
float StanceAlpha = 0.0f;
```

#### Combat Variables
```cpp
UPROPERTY(BlueprintReadOnly, Category = "Combat")
bool bIsAiming = false;

UPROPERTY(BlueprintReadOnly, Category = "Combat")
bool bIsFiring = false;

UPROPERTY(BlueprintReadOnly, Category = "Combat")
bool bIsReloading = false;

UPROPERTY(BlueprintReadOnly, Category = "Combat")
bool bIsInCombat = false;

UPROPERTY(BlueprintReadOnly, Category = "Combat")
FRotator AimRotation = FRotator::ZeroRotator;

UPROPERTY(BlueprintReadOnly, Category = "Combat")
float AimPitch = 0.0f;

UPROPERTY(BlueprintReadOnly, Category = "Combat")
float AimYaw = 0.0f;
```

#### Interaction Variables
```cpp
UPROPERTY(BlueprintReadOnly, Category = "Interaction")
bool bIsReviving = false;

UPROPERTY(BlueprintReadOnly, Category = "Interaction")
bool bIsBeingRevived = false;

UPROPERTY(BlueprintReadOnly, Category = "Interaction")
bool bIsDead = false;

UPROPERTY(BlueprintReadOnly, Category = "Interaction")
bool bIsDown = false;

UPROPERTY(BlueprintReadOnly, Category = "Interaction")
float ReviveProgress = 0.0f;
```

## Blend Spaces

### BS_Movement_Standing
- **X-Axis**: Speed (0-600)
- **Y-Axis**: Direction (-180 to 180)
- **Animations**:
  - Idle (0, 0)
  - Walk Forward (300, 0)
  - Run Forward (600, 0)
  - Walk Backward (300, 180)
  - Walk Left (300, -90)
  - Walk Right (300, 90)
  - Walk Forward-Left (300, -45)
  - Walk Forward-Right (300, 45)
  - Walk Backward-Left (300, -135)
  - Walk Backward-Right (300, 135)

### BS_Movement_Crouching
- **X-Axis**: Speed (0-300)
- **Y-Axis**: Direction (-180 to 180)
- **Animations**:
  - Crouch Idle (0, 0)
  - Crouch Walk Forward (150, 0)
  - Crouch Walk Backward (150, 180)
  - Crouch Walk Left (150, -90)
  - Crouch Walk Right (150, 90)

### BS_Movement_Prone
- **X-Axis**: Speed (0-100)
- **Y-Axis**: Direction (-180 to 180)
- **Animations**:
  - Prone Idle (0, 0)
  - Prone Crawl Forward (50, 0)
  - Prone Crawl Backward (50, 180)
  - Prone Crawl Left (50, -90)
  - Prone Crawl Right (50, 90)

### BS_Aim_Standing
- **X-Axis**: Aim Yaw (-90 to 90)
- **Y-Axis**: Aim Pitch (-45 to 45)
- **Animations**:
  - Aim Center (0, 0)
  - Aim Left (90, 0)
  - Aim Right (-90, 0)
  - Aim Up (0, 45)
  - Aim Down (0, -45)

## State Machine Transitions

### Idle to Walking
- **Condition**: Speed > 10.0
- **Transition Time**: 0.2 seconds
- **Blend Mode**: Inertialization

### Walking to Running
- **Condition**: Speed > 400.0 AND !bIsCrouching
- **Transition Time**: 0.3 seconds
- **Blend Mode**: Inertialization

### Standing to Crouching
- **Condition**: bIsCrouching == true
- **Transition Time**: 0.5 seconds
- **Blend Mode**: Custom Curve

### Crouching to Prone
- **Condition**: bIsProne == true
- **Transition Time**: 1.0 seconds
- **Blend Mode**: Custom Curve

### Any State to Peek
- **Condition**: bIsPeeking == true
- **Transition Time**: 0.3 seconds
- **Blend Mode**: Inertialization

### Any State to Combat
- **Condition**: bIsInCombat == true
- **Transition Time**: 0.2 seconds
- **Blend Mode**: Inertialization

## Animation Layers

### Base Layer (Locomotion)
- **Weight**: 1.0
- **Blend Mode**: Weighted
- **Contains**: Main state machine

### Upper Body Layer (Combat)
- **Weight**: Dynamic (0.0-1.0 based on bIsAiming)
- **Blend Mode**: Bone Mask
- **Bone Mask**: Upper body bones only
- **Contains**: Aiming and firing animations

### Additive Layer (Reactions)
- **Weight**: Dynamic
- **Blend Mode**: Additive
- **Contains**: Hit reactions, flinches, recoil

### Face Layer (Expressions)
- **Weight**: 1.0
- **Blend Mode**: Bone Mask
- **Bone Mask**: Head and face bones
- **Contains**: Facial expressions, eye tracking

## Animation Montages

### Combat Montages
```cpp
// Fire Montage
UAnimMontage* Fire_Rifle_Montage
- Slot: UpperBody
- Length: 0.2 seconds
- Loop: false
- Blend In: 0.05s
- Blend Out: 0.1s

// Reload Montages
UAnimMontage* Reload_Rifle_Montage
- Slot: FullBody
- Length: 2.5 seconds
- Loop: false
- Blend In: 0.2s
- Blend Out: 0.3s

UAnimMontage* Reload_Pistol_Montage
- Slot: UpperBody
- Length: 1.8 seconds
- Loop: false
- Blend In: 0.15s
- Blend Out: 0.2s
```

### Interaction Montages
```cpp
// Revive Montage
UAnimMontage* Revive_Ally_Montage
- Slot: FullBody
- Length: 3.0 seconds
- Loop: true
- Blend In: 0.3s
- Blend Out: 0.3s

// Death Montages
UAnimMontage* Death_Forward_Montage
UAnimMontage* Death_Backward_Montage
UAnimMontage* Death_Left_Montage
UAnimMontage* Death_Right_Montage
```

## Animation Notifies

### Combat Notifies
- **AN_FireWeapon**: Triggers weapon fire
- **AN_EjectShell**: Spawns shell casing
- **AN_ReloadComplete**: Signals reload completion
- **AN_MagazineOut**: Magazine removal point
- **AN_MagazineIn**: Magazine insertion point

### Movement Notifies
- **AN_Footstep_Left**: Left foot impact
- **AN_Footstep_Right**: Right foot impact
- **AN_StanceChange**: Stance transition complete

### Interaction Notifies
- **AN_ReviveProgress**: Update revive progress
- **AN_ReviveComplete**: Revive action complete
- **AN_InteractionStart**: Begin interaction
- **AN_InteractionEnd**: End interaction

## Blueprint Event Graph

### Event Blueprint Update Animation
```cpp
void UpdateAnimation()
{
    // Get character reference
    ASquadMateCharacter* Character = Cast<ASquadMateCharacter>(GetOwningActor());
    if (!Character) return;

    // Update movement variables
    Velocity = Character->GetVelocity();
    Speed = Velocity.Size();
    bIsMoving = Speed > 10.0f;
    
    if (bIsMoving)
    {
        Direction = CalculateDirection(Velocity, Character->GetActorRotation());
    }

    // Update stance variables
    CurrentStance = Character->GetStance();
    bIsCrouching = (CurrentStance == ECharacterStance::Crouching);
    bIsProne = (CurrentStance == ECharacterStance::Prone);
    bIsPeeking = (CurrentStance == ECharacterStance::PeekLeft || 
                  CurrentStance == ECharacterStance::PeekRight);

    // Update combat variables
    bIsAiming = Character->bIsAiming;
    bIsFiring = Character->bIsFiring;
    bIsReloading = Character->bIsReloading;
    bIsInCombat = Character->GetSquadMateAIController()->GetTacticState() == ETacticState::Engage;

    // Update aim rotation
    if (bIsAiming)
    {
        AimRotation = Character->GetAimRotation();
        AimPitch = AimRotation.Pitch;
        AimYaw = AimRotation.Yaw;
    }

    // Update interaction variables
    if (UHealthComponent* HealthComp = Character->GetHealthComponent())
    {
        bIsDead = HealthComp->IsDead();
        bIsDown = HealthComp->IsDown();
    }

    if (UReviveComponent* ReviveComp = Character->GetReviveComponent())
    {
        bIsReviving = ReviveComp->IsReviving();
        bIsBeingRevived = ReviveComp->IsBeingRevived();
        ReviveProgress = ReviveComp->GetReviveProgress();
    }
}
```

## Performance Optimization

### LOD System
- **LOD 0** (0-500 units): Full animation fidelity
- **LOD 1** (500-1000 units): Reduced update frequency
- **LOD 2** (1000+ units): Basic animations only

### Update Frequency
- **High Priority**: Combat animations (60 FPS)
- **Medium Priority**: Movement animations (30 FPS)
- **Low Priority**: Idle animations (15 FPS)

### Bone Optimization
- **High Detail**: All bones animated
- **Medium Detail**: Major bones only
- **Low Detail**: Root motion only

## Testing and Debugging

### Animation Debug Commands
```cpp
// Show animation debug info
showdebug animation

// Display bone names
showdebug bones

// Show state machine states
showdebug statemachine

// Display blend space values
showdebug blendspace
```

### Common Issues and Solutions

#### Issue: Jittery transitions
**Solution**: Increase transition time, use inertialization

#### Issue: Floating during stance changes
**Solution**: Adjust root motion settings, use custom curves

#### Issue: Aim offset not working
**Solution**: Check bone hierarchy, verify aim space setup

#### Issue: Montages not blending properly
**Solution**: Adjust slot weights, check blend settings

## Integration with AI System

### Blackboard Integration
The Animation Blueprint reads from these Blackboard keys:
- CurrentStance
- IsInCombat
- TacticState
- TargetActor (for aim direction)

### Component Communication
- **HealthComponent**: Health status, death state
- **InventoryComponent**: Weapon type, reload state
- **ReviveComponent**: Revival interactions
- **DecisionLoggerComponent**: Animation event logging
