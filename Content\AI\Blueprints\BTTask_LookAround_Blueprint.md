# BTTask_LookAround - Complete Blueprint Implementation

## 🎯 Blueprint Class Setup

### Class Configuration
- **Parent Class**: `BTTaskNode` (or `BTTask_BlueprintBase`)
- **Blueprint Name**: `BTTask_LookAround_BP`
- **Location**: `/Content/AI/BehaviorTree/Tasks/`
- **Category**: "SquadMate AI Tasks"

## 📋 Blueprint Variables

### 🔑 Blackboard Key Selectors
```cpp
// Input Pins (Category: Blackboard Keys)
LastKnownEnemyLocation (Blackboard Key Selector)
├── Key Type: Vector
├── Default: "LastKnownEnemyLocation"
└── Tooltip: "Last position where enemy was seen"

VisibleEnemies (Blackboard Key Selector)
├── Key Type: Object Array
├── Default: "VisibleEnemies"
└── Tooltip: "Currently visible enemy actors"

AlertnessLevel (Blackboard Key Selector)
├── Key Type: Float
├── Default: "AlertnessLevel"
└── Tooltip: "Current awareness level (0.0-1.0)"

SquadRole (Blackboard Key Selector)
├── Key Type: Enum
├── Default: "SquadRole"
└── Tooltip: "Agent's tactical role"
```

### 👀 Look Around Settings
```cpp
// Look Around (Category: Look Around Configuration)
LookAroundDuration (Float) = 5.0
├── Meta: (ClampMin="1.0", ClampMax="30.0")
└── Tooltip: "How long to perform look around behavior"

ScanRadius (Float) = 1000.0
├── Meta: (ClampMin="300.0", ClampMax="2000.0")
└── Tooltip: "Radius to scan for threats"

LookSpeed (Float) = 90.0
├── Meta: (ClampMin="30.0", ClampMax="180.0")
└── Tooltip: "Degrees per second rotation speed"

ScanPattern (Enum: EScanPattern) = Systematic
├── Values: Systematic, Random, ThreatFocused, PerimeterScan
└── Tooltip: "Pattern for scanning area"

PauseAtPoints (Boolean) = true
└── Tooltip: "Pause briefly at scan points"

PauseDuration (Float) = 0.5
├── Meta: (ClampMin="0.1", ClampMax="2.0")
└── Tooltip: "How long to pause at each point"
```

### 🎯 Scan Configuration
```cpp
// Scanning (Category: Scan Configuration)
NumberOfScanPoints (Integer) = 8
├── Meta: (ClampMin="4", ClampMax="16")
└── Tooltip: "Number of points to scan"

ScanArcAngle (Float) = 360.0
├── Meta: (ClampMin="90.0", ClampMax="360.0")
└── Tooltip: "Total arc to scan in degrees"

PrioritizeThreats (Boolean) = true
└── Tooltip: "Focus on known threat directions"

IncludeVerticalScan (Boolean) = false
└── Tooltip: "Include up/down scanning"

VerticalScanAngle (Float) = 30.0
├── Meta: (ClampMin="10.0", ClampMax="60.0")
└── Tooltip: "Vertical scan range in degrees"
```

### 🔍 Detection Settings
```cpp
// Detection (Category: Detection)
UseAIPerception (Boolean) = true
└── Tooltip: "Use AI Perception for detection"

DetectionRange (Float) = 1200.0
├── Meta: (ClampMin="500.0", ClampMax="3000.0")
└── Tooltip: "Maximum detection distance"

PeripheralVisionAngle (Float) = 90.0
├── Meta: (ClampMin="45.0", ClampMax="180.0")
└── Tooltip: "Peripheral vision cone angle"

UpdateBlackboardOnDetection (Boolean) = true
└── Tooltip: "Update blackboard when enemies found"

CallOutDetections (Boolean) = true
└── Tooltip: "Announce detected enemies to squad"
```

### 🎬 Animation Settings
```cpp
// Animation (Category: Animation)
LookAroundAnimation (Animation Sequence)
└── Tooltip: "Idle animation while looking around"

AlertAnimation (Animation Sequence)
└── Tooltip: "Animation when threat detected"

UseHeadTracking (Boolean) = true
└── Tooltip: "Use head bone for looking"

HeadBoneName (String) = "head"
└── Tooltip: "Name of head bone for tracking"

MaxHeadRotation (Float) = 60.0
├── Meta: (ClampMin="30.0", ClampMax="90.0")
└── Tooltip: "Maximum head rotation angle"
```

## 🔧 Event Graph Implementation

### 📍 Event Receive Execute AI
```cpp
Event Receive Execute AI
├── Sequence: Input Validation
│   ├── Get Blackboard Component
│   ├── Get Controlled Pawn
│   ├── Branch: Is Valid?
│   │   ├── False → Finish Execute (Failed)
│   │   └── True → Continue
│   └── Function Call: Initialize Look Around State
├── Custom Event: Setup Scan Pattern
└── Return Value: In Progress
```

### 👀 Setup Scan Pattern
```cpp
Custom Event: Setup Scan Pattern
├── Sequence: Pattern Configuration
│   ├── Set Variable: LookStartTime (Current Time)
│   ├── Set Variable: CurrentScanPoint (0)
│   ├── Set Variable: IsLookingAround (True)
│   └── Function Call: Generate Scan Points
├── Sequence: Animation Setup
│   ├── Function Call: Start Look Around Animation
│   ├── Function Call: Set Agent Alertness
│   └── Function Call: Configure Head Tracking
├── Sequence: Detection Setup
│   ├── Function Call: Configure AI Perception
│   ├── Function Call: Set Detection Parameters
│   └── Function Call: Enable Threat Monitoring
├── Custom Event: Begin Scanning Sequence
└── Function Call: Broadcast Look Around Start
```

### 🔍 Begin Scanning Sequence
```cpp
Custom Event: Begin Scanning Sequence
├── Sequence: Start Scan Timer
│   ├── Set Timer by Function Name
│   │   ├── Function Name: "Execute Scan Point"
│   │   ├── Time: Calculate Scan Interval
│   │   │   └── LookAroundDuration / NumberOfScanPoints
│   │   ├── Looping: True
│   │   └── Store Handle: ScanTimer
│   ├── Set Timer by Function Name
│   │   ├── Function Name: "Monitor Detection"
│   │   ├── Time: 0.1 (High frequency monitoring)
│   │   ├── Looping: True
│   │   └── Store Handle: DetectionTimer
│   └── Set Timer by Function Name
│       ├── Function Name: "Complete Look Around"
│       ├── Time: LookAroundDuration
│       ├── Looping: False
│       └── Store Handle: CompletionTimer
├── Function Call: Execute First Scan Point
└── Function Call: Log Scan Start
```

### 🎯 Execute Scan Point
```cpp
Function: Execute Scan Point
├── Sequence: Get Current Scan Target
│   ├── Get Array Element (ScanPoints, CurrentScanPoint)
│   ├── Branch: Valid Scan Point?
│   │   ├── False → Custom Event: Complete Look Around
│   │   └── True → Continue
│   └── Set Variable: CurrentTargetDirection
├── Sequence: Rotate to Scan Point
│   ├── Function Call: Calculate Required Rotation
│   │   ├── Current: Agent Forward Vector
│   │   ├── Target: Scan Point Direction
│   │   └── Output: Target Rotation
│   ├── Function Call: Start Rotation Interpolation
│   │   ├── Start Rotation: Current Agent Rotation
│   │   ├── End Rotation: Target Rotation
│   │   ├── Speed: LookSpeed
│   │   └── Smooth: True
│   └── Set Timer by Function Name
│       ├── Function Name: "Monitor Rotation Progress"
│       ├── Time: 0.05
│       ├── Looping: True
│       └── Store Handle: RotationTimer
├── Function Call: Update Head Tracking
└── Function Call: Perform Threat Scan
```

### 🔄 Monitor Rotation Progress
```cpp
Function: Monitor Rotation Progress
├── Sequence: Check Rotation Status
│   ├── Get Agent Current Rotation
│   ├── Get Target Rotation
│   ├── Calculate Rotation Difference
│   └── Branch: Rotation Complete?
│       ├── Difference < 5.0 degrees
│       ├── True → Custom Event: On Scan Point Reached
│       └── False → Continue Rotation
├── Function Call: Update Rotation Interpolation
│   ├── Lerp Rotation
│   │   ├── A: Current Rotation
│   │   ├── B: Target Rotation
│   │   ├── Alpha: Delta Time × LookSpeed
│   │   └── Output: New Rotation
│   ├── Set Agent Rotation
│   └── Update Head Bone Rotation
└── Function Call: Update Animation State
```

### ✅ On Scan Point Reached
```cpp
Custom Event: On Scan Point Reached
├── Sequence: Scan Point Processing
│   ├── Clear Timer by Handle (RotationTimer)
│   ├── Function Call: Perform Detailed Scan
│   │   ├── Line Trace in Direction
│   │   ├── Sphere Trace for Enemies
│   │   ├── Check for Movement
│   │   └── Analyze Threat Level
│   ├── Branch: Pause at Points Enabled?
│   │   ├── True → Set Timer by Function Name
│   │   │   ├── Function Name: "Advance to Next Point"
│   │   │   ├── Time: PauseDuration
│   │   │   └── Looping: False
│   │   └── False → Custom Event: Advance to Next Point
│   └── Function Call: Update Scan Progress
├── Function Call: Process Scan Results
└── Function Call: Log Scan Point Complete
```

### ➡️ Advance to Next Point
```cpp
Custom Event: Advance to Next Point
├── Sequence: Point Management
│   ├── Add to Variable: CurrentScanPoint (+1)
│   ├── Branch: More Points to Scan?
│   │   ├── CurrentScanPoint < NumberOfScanPoints
│   │   ├── True → Continue Scanning
│   │   └── False → Custom Event: Complete Scan Cycle
│   └── Function Call: Update Scan Progress UI
├── Branch: Continue Scanning?
│   ├── True → Function Call: Execute Scan Point
│   └── False → Custom Event: Complete Look Around
└── Function Call: Update Squad on Progress
```

## 🔧 Core Functions

### 🎯 Generate Scan Points
```cpp
Function: Generate Scan Points
├── Output: Scan Points Array (Array of Vectors)
├── Implementation:
│   ├── Clear Existing Scan Points
│   ├── Get Agent Current Location and Rotation
│   ├── Switch on Enum (ScanPattern)
│   │   ├── Systematic → Function Call: Generate Systematic Points
│   │   │   ├── Calculate Angle Step
│   │   │   │   └── ScanArcAngle / NumberOfScanPoints
│   │   │   ├── For Loop: Create Points
│   │   │   │   ├── Current Angle = Start Angle + (Step × Index)
│   │   │   │   ├── Direction Vector = Rotate Forward by Angle
│   │   │   │   ├── Scan Point = Agent Location + (Direction × ScanRadius)
│   │   │   │   └── Add to Scan Points Array
│   │   │   └── End Loop
│   │   ├── Random → Function Call: Generate Random Points
│   │   │   ├── For Loop: NumberOfScanPoints
│   │   │   │   ├── Random Angle = Random Float (-ScanArcAngle/2, ScanArcAngle/2)
│   │   │   │   ├── Random Distance = Random Float (ScanRadius × 0.5, ScanRadius)
│   │   │   │   ├── Direction = Rotate Forward by Random Angle
│   │   │   │   ├── Point = Agent Location + (Direction × Random Distance)
│   │   │   │   └── Add to Array
│   │   │   └── End Loop
│   │   ├── ThreatFocused → Function Call: Generate Threat-Focused Points
│   │   │   ├── Get Last Known Enemy Locations
│   │   │   ├── Weight Points Toward Threats
│   │   │   ├── Add High-Risk Direction Points
│   │   │   └── Fill Remaining with Systematic Points
│   │   └── PerimeterScan → Function Call: Generate Perimeter Points
│   │       ├── Create 360-degree Coverage
│   │       ├── Focus on Horizon Level
│   │       └── Include Escape Route Monitoring
│   ├── Branch: Include Vertical Scan?
│   │   ├── True → Function Call: Add Vertical Scan Points
│   │   │   ├── Add Upward Scan Points
│   │   │   ├── Add Downward Scan Points
│   │   │   └── Integrate with Horizontal Points
│   │   └── False → Skip Vertical
│   └── Function Call: Validate and Filter Points
│       ├── Remove Invalid Points
│       ├── Ensure Minimum Distance Between Points
│       └── Sort by Priority
```

### 🔍 Perform Detailed Scan
```cpp
Function: Perform Detailed Scan
├── Input: Scan Direction (Vector)
├── Output: Scan Results (Struct)
├── Implementation:
│   ├── Sequence: Visual Scan
│   │   ├── Line Trace by Channel
│   │   │   ├── Start: Agent Eye Location
│   │   │   ├── End: Eye Location + (Direction × DetectionRange)
│   │   │   ├── Channel: Visibility
│   │   │   └── Ignore: Self + Squad Members
│   │   ├── Multi Sphere Trace by Channel
│   │   │   ├── Start: Agent Location
│   │   │   ├── End: Agent Location + (Direction × DetectionRange)
│   │   │   ├── Radius: 100.0
│   │   │   ├── Channel: Pawn
│   │   │   └── Ignore: Self + Squad Members
│   │   └── Store Hit Results
│   ├── Sequence: AI Perception Check
│   │   ├── Branch: Use AI Perception?
│   │   │   ├── True → Get AI Perception Component
│   │   │   │   ├── Get Currently Perceived Actors
│   │   │   │   ├── Filter by Direction
│   │   │   │   └── Add to Results
│   │   │   └── False → Skip Perception
│   │   └── Update Perception Data
│   ├── Sequence: Threat Analysis
│   │   ├── For Each Hit Result
│   │   │   ├── Cast to Enemy Character
│   │   │   ├── Branch: Is Enemy?
│   │   │   │   ├── True → Function Call: Analyze Threat
│   │   │   │   │   ├── Calculate Distance
│   │   │   │   │   ├── Assess Weapon Threat
│   │   │   │   │   ├── Check Line of Sight
│   │   │   │   │   └── Determine Threat Level
│   │   │   │   └── False → Continue Loop
│   │   │   └── Add Threat to Results
│   │   └── End Loop
│   ├── Sequence: Environmental Analysis
│   │   ├── Check for Cover Opportunities
│   │   ├── Identify Potential Ambush Points
│   │   ├── Assess Escape Routes
│   │   └── Note Tactical Features
│   └── Return: Compiled Scan Results
```

### 📢 Process Scan Results
```cpp
Function: Process Scan Results
├── Input: Scan Results (Struct)
├── Implementation:
│   ├── Branch: Enemies Detected?
│   │   ├── True → Sequence: Handle Enemy Detection
│   │   │   ├── Function Call: Update Blackboard
│   │   │   │   ├── Set VisibleEnemies Array
│   │   │   │   ├── Update LastKnownEnemyLocation
│   │   │   │   └── Increase AlertnessLevel
│   │   │   ├── Function Call: Broadcast Enemy Contact
│   │   │   │   ├── Message: "Enemy spotted at [Direction]!"
│   │   │   │   ├── Priority: High
│   │   │   │   └── Include Location Data
│   │   │   ├── Function Call: Update Animation State
│   │   │   │   ├── Switch to Alert Animation
│   │   │   │   ├── Increase Movement Speed
│   │   │   │   └── Ready Weapon
│   │   │   └── Branch: Should Interrupt Scan?
│   │   │       ├── High Threat Level
│   │   │       ├── True → Custom Event: Complete Look Around
│   │   │       └── False → Continue Scan
│   │   └── False → Continue Normal Scan
│   ├── Function Call: Update Squad Intelligence
│   │   ├── Share Scan Data with Squad
│   │   ├── Update Tactical Map
│   │   └── Coordinate Squad Awareness
│   ├── Function Call: Log Scan Results
│   └── Function Call: Update Decision Logger
```

### 🎯 Configure Head Tracking
```cpp
Function: Configure Head Tracking
├── Implementation:
│   ├── Branch: Use Head Tracking?
│   │   ├── Get Variable: UseHeadTracking
│   │   ├── False → Return
│   │   └── True → Continue
│   ├── Get Character Mesh Component
│   ├── Branch: Has Valid Mesh?
│   │   ├── False → Return
│   │   └── True → Continue
│   ├── Find Bone by Name (HeadBoneName)
│   ├── Branch: Head Bone Found?
│   │   ├── False → Log Warning and Return
│   │   └── True → Continue
│   ├── Set Head Tracking Parameters
│   │   ├── Max Rotation: MaxHeadRotation
│   │   ├── Tracking Speed: LookSpeed × 0.5
│   │   ├── Smooth Tracking: True
│   │   └── Enable Head Look At
│   └── Function Call: Start Head Tracking Update
│       ├── Set Timer by Function Name
│       ├── Function Name: "Update Head Tracking"
│       ├── Time: 0.05
│       ├── Looping: True
│       └── Store Handle: HeadTrackingTimer
```

## 📊 Input/Output Pin Configuration

### 🔌 Input Execution Pins
- **Execute**: Main execution entry point
- **Abort**: Stop look around behavior

### 🔌 Output Execution Pins
- **Success**: Look around completed successfully
- **Failed**: Unable to perform look around
- **In Progress**: Currently scanning area

### 📋 Input Data Pins
- **Owner Controller**: AI Controller reference
- **Controlled Pawn**: Character performing scan
- **Focus Direction**: Optional direction to prioritize (Vector)
- **Duration Override**: Custom scan duration (Float)

### 📋 Output Data Pins
- **Enemies Found**: Number of enemies detected
- **Threat Level**: Assessed threat level (0.0-1.0)
- **Scan Coverage**: Percentage of area scanned
- **Detection Results**: Array of detected actors

This Blueprint implementation provides a comprehensive area scanning system with multiple patterns, threat detection, squad communication, and intelligent behavior adaptation!
