#pragma once

#include "CoreMinimal.h"
#include "BehaviorTree/BTService.h"
#include "AI/SquadMateAIController.h"
#include "Perception/AIPerceptionComponent.h"
#include "BTService_UpdateCombatState.generated.h"

UENUM(BlueprintType)
enum class ECombatIntensity : uint8
{
    None        UMETA(DisplayName = "None"),
    Low         UMETA(DisplayName = "Low"),
    Medium      UMETA(DisplayName = "Medium"),
    High        UMETA(DisplayName = "High"),
    Critical    UMETA(DisplayName = "Critical")
};

USTRUCT(BlueprintType)
struct FCombatStateData
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly)
    bool bHasLineOfSight = false;

    UPROPERTY(BlueprintReadOnly)
    bool bIsUnderFire = false;

    UPROPERTY(BlueprintReadOnly)
    bool bAmmoLow = false;

    UPROPERTY(BlueprintReadOnly)
    int32 VisibleEnemyCount = 0;

    UPROPERTY(BlueprintReadOnly)
    int32 NearbyAllyCount = 0;

    UPROPERTY(BlueprintReadOnly)
    float DistanceToNearestEnemy = FLT_MAX;

    UPROPERTY(BlueprintReadOnly)
    float HealthPercentage = 1.0f;

    UPROPERTY(BlueprintReadOnly)
    ECombatIntensity CombatIntensity = ECombatIntensity::None;

    UPROPERTY(BlueprintReadOnly)
    float ThreatLevel = 0.0f;

    UPROPERTY(BlueprintReadOnly)
    float LastDamageTime = 0.0f;

    UPROPERTY(BlueprintReadOnly)
    FVector LastKnownEnemyLocation = FVector::ZeroVector;

    FCombatStateData()
    {
        bHasLineOfSight = false;
        bIsUnderFire = false;
        bAmmoLow = false;
        VisibleEnemyCount = 0;
        NearbyAllyCount = 0;
        DistanceToNearestEnemy = FLT_MAX;
        HealthPercentage = 1.0f;
        CombatIntensity = ECombatIntensity::None;
        ThreatLevel = 0.0f;
        LastDamageTime = 0.0f;
        LastKnownEnemyLocation = FVector::ZeroVector;
    }
};

/**
 * Behavior Tree service that continuously updates combat-related blackboard keys
 * Monitors line of sight, threat levels, ammo status, and tactical situation
 */
UCLASS(BlueprintType, meta=(DisplayName="Update Combat State"))
class SQUADMATEAI_API UBTService_UpdateCombatState : public UBTService
{
    GENERATED_BODY()

public:
    UBTService_UpdateCombatState();

protected:
    virtual void TickNode(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, float DeltaSeconds) override;
    virtual void OnBecomeRelevant(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory) override;
    virtual void OnCeaseRelevant(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory) override;
    virtual FString GetStaticDescription() const override;

    // Blackboard Keys
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    struct FBlackboardKeySelector TargetActorKey;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    struct FBlackboardKeySelector HasLineOfSightKey;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    struct FBlackboardKeySelector IsUnderFireKey;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    struct FBlackboardKeySelector AmmoLowKey;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    struct FBlackboardKeySelector HealthPercentageKey;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    struct FBlackboardKeySelector CombatIntensityKey;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    struct FBlackboardKeySelector LastKnownEnemyLocationKey;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    struct FBlackboardKeySelector VisibleEnemiesKey;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    struct FBlackboardKeySelector NearbyAlliesKey;

    // Line of Sight Configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Line of Sight")
    bool bCheckLineOfSight = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Line of Sight")
    float LineOfSightRange = 1500.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Line of Sight")
    TEnumAsByte<ECollisionChannel> LineOfSightChannel = ECC_Visibility;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Line of Sight")
    bool bUseMultipleTracePoints = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Line of Sight")
    TArray<FVector> TraceOffsets;

    // Threat Detection
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Threat Detection")
    float UnderFireDetectionTime = 2.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Threat Detection")
    float ThreatDecayRate = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Threat Detection")
    float MaxThreatDistance = 1000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Threat Detection")
    bool bDetectProjectiles = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Threat Detection")
    float ProjectileDetectionRadius = 300.0f;

    // Ammo Monitoring
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Ammo")
    int32 LowAmmoThreshold = 10;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Ammo")
    float LowAmmoPercentage = 0.3f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Ammo")
    bool bCheckReserveAmmo = true;

    // Combat Intensity Calculation
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat Intensity")
    float IntensityEnemyWeight = 2.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat Intensity")
    float IntensityDistanceWeight = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat Intensity")
    float IntensityHealthWeight = 1.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat Intensity")
    float IntensityAmmoWeight = 1.0f;

    // Ally Detection
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Ally Detection")
    float AllyDetectionRadius = 800.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Ally Detection")
    bool bOnlyCountActiveAllies = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Ally Detection")
    bool bIncludeDownedAllies = false;

    // Performance Settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bUseOptimizedUpdates = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float HighPriorityUpdateRate = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float LowPriorityUpdateRate = 0.5f;

    // Debug Options
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bDrawDebugInfo = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bLogStateChanges = false;

public:
    // Public interface for manual updates
    UFUNCTION(BlueprintCallable, Category = "Combat State")
    static FCombatStateData GetCombatState(AActor* Agent);

    UFUNCTION(BlueprintCallable, Category = "Combat State")
    static bool HasLineOfSightToTarget(AActor* Agent, AActor* Target);

    UFUNCTION(BlueprintCallable, Category = "Combat State")
    static ECombatIntensity CalculateCombatIntensity(AActor* Agent, const TArray<AActor*>& Enemies);

protected:
    // Core update functions
    void UpdateLineOfSight(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory);
    void UpdateThreatDetection(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory);
    void UpdateAmmoStatus(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory);
    void UpdateHealthStatus(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory);
    void UpdateCombatIntensity(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory);
    void UpdateEnemyTracking(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory);
    void UpdateAllyTracking(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory);

    // Line of sight calculations
    bool PerformLineOfSightCheck(AActor* Agent, AActor* Target);
    bool PerformMultiPointLineOfSightCheck(AActor* Agent, AActor* Target);
    FVector GetOptimalTraceStart(AActor* Agent);
    FVector GetOptimalTraceEnd(AActor* Target);

    // Threat analysis
    bool IsUnderFire(AActor* Agent, float TimeWindow);
    float CalculateThreatLevel(AActor* Agent, const TArray<AActor*>& Enemies);
    bool DetectNearbyProjectiles(AActor* Agent, float Radius);
    void UpdateLastDamageTime(AActor* Agent);

    // Enemy and ally detection
    TArray<AActor*> GetVisibleEnemies(AActor* Agent);
    TArray<AActor*> GetNearbyAllies(AActor* Agent, float Radius);
    bool IsValidEnemy(AActor* Agent, AActor* PotentialEnemy);
    bool IsValidAlly(AActor* Agent, AActor* PotentialAlly);

    // Combat intensity calculation
    ECombatIntensity DetermineCombatIntensity(float IntensityScore);
    float CalculateIntensityScore(AActor* Agent, const FCombatStateData& StateData);
    void ApplyIntensityModifiers(float& IntensityScore, AActor* Agent);

    // Blackboard updates
    void UpdateBlackboardKeys(UBehaviorTreeComponent& OwnerComp, const FCombatStateData& StateData);
    void SetBlackboardValue(UBehaviorTreeComponent& OwnerComp, const FBlackboardKeySelector& Key, bool Value);
    void SetBlackboardValue(UBehaviorTreeComponent& OwnerComp, const FBlackboardKeySelector& Key, float Value);
    void SetBlackboardValue(UBehaviorTreeComponent& OwnerComp, const FBlackboardKeySelector& Key, int32 Value);
    void SetBlackboardValue(UBehaviorTreeComponent& OwnerComp, const FBlackboardKeySelector& Key, const FVector& Value);

    // Performance optimization
    bool ShouldPerformHighPriorityUpdate(uint8* NodeMemory, float CurrentTime);
    bool ShouldPerformLowPriorityUpdate(uint8* NodeMemory, float CurrentTime);
    void OptimizeUpdateFrequency(uint8* NodeMemory, const FCombatStateData& StateData);

    // Utility functions
    float GetDistanceToActor(AActor* From, AActor* To);
    bool IsActorValid(AActor* Actor);
    ASquadMateAIController* GetSquadMateController(UBehaviorTreeComponent& OwnerComp);

    // Debug and logging
    void LogStateChange(const FString& StateChange, UBehaviorTreeComponent& OwnerComp);
    void DrawDebugCombatInfo(UWorld* World, AActor* Agent, const FCombatStateData& StateData);

private:
    // Service memory structure
    struct FBTService_UpdateCombatStateMemory
    {
        FCombatStateData LastStateData;
        float LastHighPriorityUpdate = 0.0f;
        float LastLowPriorityUpdate = 0.0f;
        float LastLineOfSightCheck = 0.0f;
        float LastThreatCheck = 0.0f;
        TArray<TWeakObjectPtr<AActor>> CachedEnemies;
        TArray<TWeakObjectPtr<AActor>> CachedAllies;
        bool bInitialized = false;
    };

    // Memory management
    void InitializeServiceMemory(uint8* NodeMemory);
    void CleanupServiceMemory(uint8* NodeMemory);
    FBTService_UpdateCombatStateMemory* GetServiceMemory(uint8* NodeMemory);

    // State comparison and change detection
    bool HasStateChanged(const FCombatStateData& NewState, const FCombatStateData& OldState);
    void ProcessStateChanges(UBehaviorTreeComponent& OwnerComp, const FCombatStateData& NewState, 
                           const FCombatStateData& OldState);

    // Cache management
    void UpdateEnemyCache(uint8* NodeMemory, const TArray<AActor*>& Enemies);
    void UpdateAllyCache(uint8* NodeMemory, const TArray<AActor*>& Allies);
    void CleanupInvalidActors(TArray<TWeakObjectPtr<AActor>>& ActorArray);

    // Default trace offsets for multi-point line of sight
    void InitializeDefaultTraceOffsets();
};
