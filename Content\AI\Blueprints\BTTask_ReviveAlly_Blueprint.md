# BTTask_ReviveAlly - Complete Blueprint Implementation

## 🎯 Blueprint Class Setup

### Class Configuration
- **Parent Class**: `BTTaskNode` (or `BTTask_BlueprintBase`)
- **Blueprint Name**: `BTTask_ReviveAlly_BP`
- **Location**: `/Content/AI/BehaviorTree/Tasks/`
- **Category**: "SquadMate AI Tasks"

## 📋 Blueprint Variables

### 🔑 Blackboard Key Selectors
```cpp
// Input Pins (Category: Blackboard Keys)
ReviveTarget (Blackboard Key Selector)
├── Key Type: Object (Actor)
├── Default: "ReviveTarget"
└── Tooltip: "Ally that needs revival"

IsReviving (Blackboard Key Selector)
├── Key Type: Bool
├── Default: "IsReviving"
└── Tooltip: "Whether currently performing revival"

IsUnderFire (Blackboard Key Selector)
├── Key Type: Bool
├── Default: "IsUnderFire"
└── Tooltip: "Safety check for revival"

SquadRole (Blackboard Key Selector)
├── Key Type: Enum
├── Default: "SquadRole"
└── Tooltip: "Agent's current role"
```

### ⚙️ Configuration Variables
```cpp
// Revive Settings (Category: Revive Configuration)
MaxReviveDistance (Float) = 200.0
├── Meta: (ClampMin="50.0", ClampMax="500.0")
└── Tooltip: "Maximum distance to attempt revival"

ReviveTime (Float) = 3.0
├── Meta: (ClampMin="1.0", ClampMax="10.0")
└── Tooltip: "Time required to complete revival"

SafetyCheckRadius (Float) = 400.0
├── Meta: (ClampMin="100.0", ClampMax="1000.0")
└── Tooltip: "Radius to check for enemies during revival"

RequireLineOfSight (Boolean) = false
└── Tooltip: "Whether clear line of sight is required"

AbortIfUnderFire (Boolean) = true
└── Tooltip: "Abort revival if taking damage"
```

### 🎬 Animation & Effects
```cpp
// Animation (Category: Animation)
ReviveMontage (Animation Montage)
└── Tooltip: "Animation to play during revival"

ReviveEffect (Particle System)
└── Tooltip: "Visual effect during revival"

ReviveSound (Sound Base)
└── Tooltip: "Audio cue for revival action"

ReviveCompleteSound (Sound Base)
└── Tooltip: "Audio cue when revival completes"
```

### 📢 Communication
```cpp
// Communication (Category: Communication)
CallOutReviveStart (Boolean) = true
└── Tooltip: "Announce when starting revival"

CallOutReviveComplete (Boolean) = true
└── Tooltip: "Announce when revival completes"

ReviveStartMessage (String) = "Reviving teammate!"
└── Tooltip: "Message to broadcast on revival start"

ReviveCompleteMessage (String) = "Teammate back up!"
└── Tooltip: "Message to broadcast on completion"
```

### 🐛 Debug Options
```cpp
// Debug (Category: Debug)
EnableDebugDraw (Boolean) = false
└── Tooltip: "Show debug visualization"

LogReviveEvents (Boolean) = true
└── Tooltip: "Log revival events to console"

DebugDrawDuration (Float) = 3.0
└── Tooltip: "How long to show debug info"
```

## 🔧 Event Graph Implementation

### 📍 Event Receive Execute AI
```cpp
Event Receive Execute AI
├── Sequence: Validate Inputs
│   ├── Get Blackboard Component
│   ├── Get Controlled Pawn
│   ├── Branch: Is Valid?
│   │   ├── False → Finish Execute (Failed)
│   │   └── True → Continue
│   └── Get Blackboard Value as Object (ReviveTarget)
├── Branch: Has Valid Target?
│   ├── False → Finish Execute (Failed)
│   └── True → Continue
├── Custom Event: Start Revive Process
└── Return Value: In Progress
```

### 🎯 Start Revive Process
```cpp
Custom Event: Start Revive Process
├── Sequence: Safety Validation
│   ├── Function Call: Check Revive Distance
│   ├── Function Call: Check Safety Conditions
│   ├── Branch: Is Safe to Revive?
│   │   ├── False → Finish Execute (Failed)
│   │   └── True → Continue
│   └── Function Call: Move to Revive Position
├── Sequence: Begin Revival
│   ├── Set Blackboard Value as Bool (IsReviving = True)
│   ├── Function Call: Play Revive Animation
│   ├── Function Call: Broadcast Revive Start
│   ├── Function Call: Start Revive Effects
│   └── Set Timer by Function Name
│       ├── Function Name: "Update Revive Progress"
│       ├── Time: 0.1 (Update frequency)
│       ├── Looping: True
│       └── Store Handle: ReviveUpdateTimer
└── Set Timer by Function Name
    ├── Function Name: "Complete Revive"
    ├── Time: ReviveTime
    ├── Looping: False
    └── Store Handle: ReviveCompleteTimer
```

### 🔄 Update Revive Progress
```cpp
Function: Update Revive Progress
├── Sequence: Continuous Safety Check
│   ├── Function Call: Check Safety Conditions
│   ├── Branch: Still Safe?
│   │   ├── False → Custom Event: Abort Revive
│   │   └── True → Continue
│   ├── Function Call: Check Target Still Valid
│   ├── Branch: Target Still Down?
│   │   ├── False → Custom Event: Abort Revive
│   │   └── True → Continue
│   └── Function Call: Update Revive Visual Progress
├── Sequence: Distance Check
│   ├── Get Distance Between Actors
│   │   ├── Actor A: Controlled Pawn
│   │   └── Actor B: Revive Target
│   ├── Branch: Within Range?
│   │   ├── False → Custom Event: Abort Revive
│   │   └── True → Continue
│   └── Function Call: Maintain Revive Position
└── Branch: Debug Enabled?
    ├── True → Function Call: Draw Debug Info
    └── False → (No Action)
```

### ✅ Complete Revive
```cpp
Function: Complete Revive
├── Sequence: Finalize Revival
│   ├── Clear Timer by Handle (ReviveUpdateTimer)
│   ├── Set Blackboard Value as Bool (IsReviving = False)
│   ├── Function Call: Stop Revive Animation
│   ├── Function Call: Stop Revive Effects
│   └── Function Call: Execute Target Revival
├── Sequence: Success Feedback
│   ├── Function Call: Broadcast Revive Complete
│   ├── Function Call: Play Success Sound
│   ├── Function Call: Log Revive Success
│   └── Function Call: Update Agent Stats
├── Sequence: Cleanup
│   ├── Clear Blackboard Value (ReviveTarget)
│   ├── Function Call: Reset Agent Stance
│   └── Function Call: Clear Debug Visuals
└── Finish Execute (Success)
```

### ❌ Abort Revive
```cpp
Custom Event: Abort Revive
├── Sequence: Emergency Cleanup
│   ├── Clear Timer by Handle (ReviveUpdateTimer)
│   ├── Clear Timer by Handle (ReviveCompleteTimer)
│   ├── Set Blackboard Value as Bool (IsReviving = False)
│   ├── Function Call: Stop Revive Animation
│   └── Function Call: Stop Revive Effects
├── Sequence: Failure Feedback
│   ├── Function Call: Broadcast Revive Failed
│   ├── Function Call: Log Revive Failure
│   └── Function Call: Play Failure Sound
├── Sequence: Safety Response
│   ├── Branch: Under Fire?
│   │   ├── True → Function Call: Seek Emergency Cover
│   │   └── False → (No Action)
│   └── Function Call: Reset Agent Stance
└── Finish Execute (Failed)
```

## 🔧 Custom Functions

### 🎯 Check Revive Distance
```cpp
Function: Check Revive Distance
├── Input: Target Actor (Actor Reference)
├── Output: Is In Range (Boolean)
├── Implementation:
│   ├── Get Distance Between Actors
│   │   ├── Actor A: Controlled Pawn
│   │   └── Actor B: Target Actor
│   ├── Branch: Distance <= MaxReviveDistance?
│   │   ├── True → Return True
│   │   └── False → Return False
└── Return Value: Boolean
```

### 🛡️ Check Safety Conditions
```cpp
Function: Check Safety Conditions
├── Input: Check Radius (Float)
├── Output: Is Safe (Boolean)
├── Implementation:
│   ├── Branch: Abort If Under Fire Enabled?
│   │   ├── True → Check Blackboard (IsUnderFire)
│   │   │   ├── True → Return False
│   │   │   └── False → Continue
│   │   └── False → Continue
│   ├── Multi Sphere Trace by Channel
│   │   ├── Start: Controlled Pawn Location
│   │   ├── End: Controlled Pawn Location
│   │   ├── Radius: SafetyCheckRadius
│   │   ├── Channel: Pawn
│   │   └── Actors to Ignore: Self + Allies
│   ├── For Each Loop (Hit Results)
│   │   ├── Cast to Enemy Character
│   │   ├── Branch: Is Enemy?
│   │   │   ├── True → Return False
│   │   │   └── False → Continue Loop
│   │   └── End Loop
│   └── Return True
└── Return Value: Boolean
```

### 🎬 Play Revive Animation
```cpp
Function: Play Revive Animation
├── Implementation:
│   ├── Get Controlled Pawn
│   ├── Cast to SquadMate Character
│   ├── Branch: Is Valid?
│   │   ├── True → Continue
│   │   └── False → Return
│   ├── Branch: Has Revive Montage?
│   │   ├── True → Play Animation Montage
│   │   │   ├── Montage: ReviveMontage
│   │   │   ├── Play Rate: 1.0
│   │   │   └── Return Value: (Store for later)
│   │   └── False → Use Default Animation
│   └── Set Character Stance (Crouching)
```

### 🎨 Start Revive Effects
```cpp
Function: Start Revive Effects
├── Implementation:
│   ├── Branch: Has Revive Effect?
│   │   ├── True → Spawn Emitter at Location
│   │   │   ├── Emitter Template: ReviveEffect
│   │   │   ├── Location: Target Actor Location
│   │   │   ├── Rotation: (0,0,0)
│   │   │   └── Auto Destroy: False
│   │   └── False → Skip Effect
│   ├── Branch: Has Revive Sound?
│   │   ├── True → Play Sound at Location
│   │   │   ├── Sound: ReviveSound
│   │   │   ├── Location: Target Actor Location
│   │   │   └── Volume: 0.7
│   │   └── False → Skip Sound
│   └── Function Call: Create Progress UI
```

### 📢 Broadcast Revive Start
```cpp
Function: Broadcast Revive Start
├── Implementation:
│   ├── Branch: Call Out Enabled?
│   │   ├── True → Continue
│   │   └── False → Return
│   ├── Get Squad Manager
│   ├── Branch: Has Squad Manager?
│   │   ├── True → Call Function: Broadcast Message
│   │   │   ├── Message: ReviveStartMessage
│   │   │   ├── Sender: Self
│   │   │   └── Priority: High
│   │   └── False → Print String (Local)
│   └── Function Call: Update Decision Logger
```

### 🏥 Execute Target Revival
```cpp
Function: Execute Target Revival
├── Input: Target Actor (Actor Reference)
├── Implementation:
│   ├── Cast to SquadMate Character
│   ├── Branch: Is Valid Character?
│   │   ├── True → Continue
│   │   └── False → Return
│   ├── Get Health Component
│   ├── Branch: Has Health Component?
│   │   ├── True → Call Function: Revive
│   │   └── False → Return
│   ├── Get Revive Component
│   ├── Branch: Has Revive Component?
│   │   ├── True → Call Function: Complete Revival
│   │   └── False → Return
│   └── Function Call: Play Revival Success Effect
```

## 🎮 Event Receive Tick AI (Optional)
```cpp
Event Receive Tick AI
├── Branch: Is Currently Reviving?
│   ├── Get Blackboard Value as Bool (IsReviving)
│   ├── True → Continue Tick Logic
│   └── False → Return (Skip Tick)
├── Sequence: Continuous Monitoring
│   ├── Function Call: Update Revive Progress Bar
│   ├── Function Call: Monitor Target Health
│   ├── Function Call: Check Environmental Hazards
│   └── Function Call: Update Animation State
└── Branch: Debug Enabled?
    ├── True → Function Call: Update Debug Display
    └── False → (No Action)
```

## 🐛 Debug Functions

### 📊 Draw Debug Info
```cpp
Function: Draw Debug Info
├── Implementation:
│   ├── Draw Debug Sphere
│   │   ├── Center: Target Actor Location
│   │   ├── Radius: 100.0
│   │   ├── Color: Green (Safe) / Red (Unsafe)
│   │   └── Duration: DebugDrawDuration
│   ├── Draw Debug Line
│   │   ├── Start: Controlled Pawn Location
│   │   ├── End: Target Actor Location
│   │   ├── Color: Blue
│   │   └── Thickness: 3.0
│   ├── Draw Debug String
│   │   ├── Text: "Reviving: [Target Name]"
│   │   ├── Location: Above Controlled Pawn
│   │   ├── Color: White
│   │   └── Duration: DebugDrawDuration
│   └── Draw Debug Sphere (Safety Radius)
│       ├── Center: Controlled Pawn Location
│       ├── Radius: SafetyCheckRadius
│       ├── Color: Yellow (Transparent)
│       └── Duration: DebugDrawDuration
```

## 📊 Input/Output Pin Configuration

### 🔌 Input Execution Pins
- **Execute**: Main execution entry point
- **Abort**: Emergency abort execution

### 🔌 Output Execution Pins
- **Success**: Revival completed successfully
- **Failed**: Revival failed or was aborted
- **In Progress**: Revival is currently active

### 📋 Input Data Pins
- **Owner Controller**: AI Controller reference
- **Controlled Pawn**: Character being controlled
- **Revive Target**: Actor to revive (from Blackboard)

### 📋 Output Data Pins
- **Revive Result**: Enum indicating success/failure reason
- **Time Taken**: Float indicating actual revival duration
- **Safety Status**: Boolean indicating if area was safe

This Blueprint implementation provides a complete, production-ready revival system with safety checks, animations, effects, communication, and comprehensive error handling!
