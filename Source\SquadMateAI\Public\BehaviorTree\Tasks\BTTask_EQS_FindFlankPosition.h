#pragma once

#include "CoreMinimal.h"
#include "BehaviorTree/BTTaskNode.h"
#include "EnvironmentQuery/EnvQueryTypes.h"
#include "AI/SquadMateAIController.h"
#include "BTTask_EQS_FindFlankPosition.generated.h"

UENUM(BlueprintType)
enum class EFlankType : uint8
{
    LeftFlank       UMETA(DisplayName = "Left Flank"),
    RightFlank      UMETA(DisplayName = "Right Flank"),
    RearFlank       UMETA(DisplayName = "Rear Flank"),
    OptimalFlank    UMETA(DisplayName = "Optimal Flank"),
    AnyFlank        UMETA(DisplayName = "Any Available Flank")
};

UENUM(BlueprintType)
enum class EFlankPriority : uint8
{
    Speed           UMETA(DisplayName = "Speed Priority"),
    Stealth         UMETA(DisplayName = "Stealth Priority"),
    Damage          UMETA(DisplayName = "Damage Priority"),
    Safety          UMETA(DisplayName = "Safety Priority"),
    Coordination    UMETA(DisplayName = "Coordination Priority")
};

USTRUCT(BlueprintType)
struct FFlankingParameters
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float MinFlankDistance = 400.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float MaxFlankDistance = 1200.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float OptimalFlankAngle = 90.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float FlankAngleTolerance = 45.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float CoverRequirement = 0.7f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float StealthFactor = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bRequireLineOfSight = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bAvoidAllyPositions = true;

    FFlankingParameters()
    {
        MinFlankDistance = 400.0f;
        MaxFlankDistance = 1200.0f;
        OptimalFlankAngle = 90.0f;
        FlankAngleTolerance = 45.0f;
        CoverRequirement = 0.7f;
        StealthFactor = 0.5f;
        bRequireLineOfSight = true;
        bAvoidAllyPositions = true;
    }
};

/**
 * Behavior Tree task that uses EQS to find optimal flanking positions
 * Considers enemy facing direction, cover availability, and tactical advantage
 */
UCLASS(BlueprintType, meta=(DisplayName="EQS Find Flank Position"))
class SQUADMATEAI_API UBTTask_EQS_FindFlankPosition : public UBTTaskNode
{
    GENERATED_BODY()

public:
    UBTTask_EQS_FindFlankPosition();

protected:
    virtual EBTNodeResult::Type ExecuteTask(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory) override;
    virtual void OnTaskFinished(UBehaviorTreeComponent& OwnerComp, uint8* NodeMemory, EBTNodeResult::Type TaskResult) override;
    virtual FString GetStaticDescription() const override;

    // EQS Configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "EQS")
    class UEnvQuery* FlankQuery;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "EQS")
    class UEnvQuery* StealthFlankQuery;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "EQS")
    class UEnvQuery* AggressiveFlankQuery;

    // Blackboard Keys
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    struct FBlackboardKeySelector TargetActorKey;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    struct FBlackboardKeySelector FlankLocationKey;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    struct FBlackboardKeySelector SquadRoleKey;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blackboard")
    struct FBlackboardKeySelector TacticStateKey;

    // Flanking Configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flanking")
    EFlankType PreferredFlankType = EFlankType::OptimalFlank;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flanking")
    EFlankPriority FlankPriority = EFlankPriority::Damage;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flanking")
    FFlankingParameters FlankingParams;

    // Role-Based Adjustments
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Role Adjustments")
    TMap<ESquadRole, FFlankingParameters> RoleBasedParams;

    // Query Parameters
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Query")
    float QueryRadius = 1500.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Query")
    int32 MaxResults = 5;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Query")
    float QueryTimeLimit = 2.0f;

    // Movement Configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement")
    bool bMoveToFlankPosition = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement")
    float MovementSpeed = 400.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement")
    bool bUseCrouchedMovement = true;

    // Coordination
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Coordination")
    bool bCoordinateWithSquad = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Coordination")
    float MinDistanceFromAllies = 300.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Coordination")
    bool bAvoidCrossfire = true;

    // Communication
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Communication")
    bool bCallOutFlankAttempt = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Communication")
    FString FlankCallout = TEXT("Flanking enemy position!");

    // Debug Options
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bDrawDebugInfo = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    float DebugDrawDuration = 5.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bLogFlankingDecisions = true;

public:
    // Static utility functions
    UFUNCTION(BlueprintCallable, Category = "Flanking", CallInEditor = true)
    static bool IsValidFlankPosition(const FVector& FlankPosition, const FVector& EnemyPosition, 
                                   const FVector& EnemyForward, float MinAngle = 45.0f);

    UFUNCTION(BlueprintCallable, Category = "Flanking", CallInEditor = true)
    static float CalculateFlankAngle(const FVector& FlankPosition, const FVector& EnemyPosition, 
                                   const FVector& EnemyForward);

    UFUNCTION(BlueprintCallable, Category = "Flanking", CallInEditor = true)
    static EFlankType DetermineFlankType(const FVector& FlankPosition, const FVector& EnemyPosition, 
                                        const FVector& EnemyForward);

    UFUNCTION(BlueprintCallable, Category = "Flanking", CallInEditor = true)
    static float ScoreFlankPosition(const FVector& FlankPosition, const FVector& EnemyPosition, 
                                  const FVector& AgentPosition, const FFlankingParameters& Params);

protected:
    // Core flanking logic
    void StartFlankQuery(UBehaviorTreeComponent& OwnerComp);
    void OnEQSQueryComplete(TSharedPtr<FEnvQueryResult> Result, UBehaviorTreeComponent* OwnerComp);

    // Query selection and configuration
    UEnvQuery* SelectOptimalQuery(UBehaviorTreeComponent& OwnerComp);
    void ConfigureQueryParams(FEnvQueryRequest& QueryRequest, UBehaviorTreeComponent& OwnerComp);
    FFlankingParameters GetAdjustedParams(UBehaviorTreeComponent& OwnerComp);

    // Position evaluation and selection
    FVector SelectBestFlankPosition(const TArray<FVector>& Candidates, UBehaviorTreeComponent& OwnerComp);
    float EvaluateFlankPosition(const FVector& Position, UBehaviorTreeComponent& OwnerComp);
    bool MeetsFlankRequirements(const FVector& Position, UBehaviorTreeComponent& OwnerComp);

    // Tactical analysis
    float CalculateTacticalAdvantage(const FVector& FlankPosition, const FVector& EnemyPosition, 
                                   const FVector& EnemyForward);
    float CalculateCoverScore(const FVector& Position, const FVector& EnemyPosition);
    float CalculateStealthScore(const FVector& Position, const TArray<FVector>& EnemyPositions);
    float CalculateCoordinationScore(const FVector& Position, const TArray<FVector>& AllyPositions);

    // Enemy analysis
    FVector GetEnemyPosition(UBehaviorTreeComponent& OwnerComp);
    FVector GetEnemyForwardVector(UBehaviorTreeComponent& OwnerComp);
    TArray<FVector> GetNearbyEnemyPositions(UBehaviorTreeComponent& OwnerComp);
    bool IsEnemyAwareOfPosition(const FVector& Position, AActor* Enemy);

    // Squad coordination
    TArray<FVector> GetSquadMemberPositions(UBehaviorTreeComponent& OwnerComp);
    bool WouldCreateCrossfire(const FVector& FlankPosition, const TArray<FVector>& AllyPositions, 
                            const FVector& EnemyPosition);
    void NotifySquadOfFlankAttempt(UBehaviorTreeComponent& OwnerComp, const FVector& FlankPosition);

    // Movement and positioning
    void InitiateFlankMovement(UBehaviorTreeComponent& OwnerComp, const FVector& FlankPosition);
    bool IsFlankPositionReachable(const FVector& Position, AActor* Agent);
    float CalculateMovementTime(const FVector& From, const FVector& To, float Speed);

    // Role-based behavior
    FFlankingParameters GetRoleBasedParameters(ESquadRole Role);
    EFlankPriority GetRolePriority(ESquadRole Role);
    void AdjustParametersForRole(FFlankingParameters& Params, ESquadRole Role);

    // Utility functions
    float GetDistanceBetweenPoints(const FVector& A, const FVector& B);
    bool HasLineOfSight(const FVector& From, const FVector& To, UWorld* World);
    FVector ProjectPointOntoPlane(const FVector& Point, const FVector& PlaneNormal, const FVector& PlanePoint);

    // Debug and visualization
    void DrawDebugFlankInfo(UWorld* World, const FVector& FlankPosition, const FVector& EnemyPosition, 
                          float Score, bool bIsSelected = false);
    void LogFlankingDecision(const FString& Decision, UBehaviorTreeComponent& OwnerComp, 
                           const FVector& Position = FVector::ZeroVector);

private:
    // Task memory structure
    struct FBTTask_FindFlankPositionMemory
    {
        TWeakObjectPtr<UBehaviorTreeComponent> OwnerComponent;
        FEnvQueryRequest QueryRequest;
        bool bQueryInProgress = false;
        FVector SelectedFlankPosition = FVector::ZeroVector;
        float TaskStartTime = 0.0f;
        EFlankType AttemptedFlankType = EFlankType::OptimalFlank;
        TArray<FVector> EvaluatedPositions;
        float BestScore = 0.0f;
    };

    // Memory management
    void InitializeTaskMemory(uint8* NodeMemory);
    void CleanupTaskMemory(uint8* NodeMemory);
    FBTTask_FindFlankPositionMemory* GetTaskMemory(uint8* NodeMemory);

    // Query result processing
    void ProcessQueryResults(const TArray<FVector>& Results, UBehaviorTreeComponent& OwnerComp);
    void FilterValidFlankPositions(TArray<FVector>& Positions, UBehaviorTreeComponent& OwnerComp);
    void SortPositionsByScore(TArray<FVector>& Positions, UBehaviorTreeComponent& OwnerComp);

    // Flank type specific logic
    void HandleLeftFlank(TArray<FVector>& Positions, const FVector& EnemyPos, const FVector& EnemyForward);
    void HandleRightFlank(TArray<FVector>& Positions, const FVector& EnemyPos, const FVector& EnemyForward);
    void HandleRearFlank(TArray<FVector>& Positions, const FVector& EnemyPos, const FVector& EnemyForward);
    void HandleOptimalFlank(TArray<FVector>& Positions, UBehaviorTreeComponent& OwnerComp);

    // Performance optimization
    bool ShouldUseDetailedAnalysis(UBehaviorTreeComponent& OwnerComp);
    void CacheFlankingData(uint8* NodeMemory, UBehaviorTreeComponent& OwnerComp);
    bool CanReuseLastFlankPosition(UBehaviorTreeComponent& OwnerComp);

    // Cached data for performance
    static TMap<AActor*, FVector> CachedEnemyPositions;
    static TMap<AActor*, FVector> CachedEnemyForwards;
    static float LastCacheUpdate;
    static const float CacheValidityDuration;
};
