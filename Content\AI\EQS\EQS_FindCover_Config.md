# EQS_FindCover Configuration Guide

This document outlines the configuration for the Environmental Query System (EQS) query used to find cover positions for SquadMate AI agents.

## Query Overview

**EQS_FindCover** is designed to find tactical cover positions that provide:
- Protection from enemy fire
- Good visibility of the battlefield
- Strategic positioning relative to squad members
- Accessibility for the AI agent

## Generator Configuration

### Grid Generator
- **Grid Size**: 20x20 (400 points)
- **Space Between**: 100 units
- **Generation Method**: Around Querier
- **Max Distance**: 1500 units
- **Min Distance**: 200 units

### Points on Circle Generator (Alternative)
- **Circle Radius**: 800 units
- **Points Count**: 32
- **Arc Direction**: Forward
- **Arc Angle**: 180 degrees

## Test Configuration

### 1. Distance to Enemy
- **Test Purpose**: Ensure cover is not too close to enemies
- **Filter Type**: Range
- **Min Value**: 300 units
- **Max Value**: 1200 units
- **Scoring**: Distance (Higher is better)
- **Weight**: 0.3

### 2. Line of Sight to Enemy
- **Test Purpose**: Prefer positions with tactical visibility
- **Filter Type**: Boolean
- **Line Trace**: Visibility Channel
- **From Context**: Querier
- **To Context**: Enemy Target
- **Scoring**: Boolean (True = 1.0, False = 0.0)
- **Weight**: 0.2

### 3. Cover Quality
- **Test Purpose**: Evaluate actual cover protection
- **Filter Type**: Range
- **Custom Test**: Cover Height Check
- **Min Cover Height**: 120 units
- **Preferred Height**: 180 units
- **Scoring**: Custom (Based on cover effectiveness)
- **Weight**: 0.4

### 4. Distance to Allies
- **Test Purpose**: Maintain squad cohesion without clustering
- **Filter Type**: Range
- **Min Distance**: 150 units
- **Max Distance**: 500 units
- **Scoring**: Inverse Distance (Moderate distance preferred)
- **Weight**: 0.15

### 5. Pathfinding
- **Test Purpose**: Ensure position is reachable
- **Filter Type**: Boolean
- **Path Mode**: Navigation Mesh
- **Max Path Length**: 2000 units
- **Scoring**: Boolean (Reachable = 1.0)
- **Weight**: 0.25

### 6. Tactical Advantage
- **Test Purpose**: Prefer elevated or strategically superior positions
- **Filter Type**: Range
- **Height Difference**: Relative to enemy
- **Preferred Height**: +50 to +200 units
- **Scoring**: Height Advantage
- **Weight**: 0.2

## Context Configuration

### Enemy Target Context
- **Context Type**: Actor
- **Source**: Blackboard Key "TargetActor"
- **Fallback**: Last Known Enemy Location

### Squad Members Context
- **Context Type**: Actors Set
- **Source**: Squad Manager
- **Filter**: Same Team ID
- **Max Distance**: 1000 units

### Cover Objects Context
- **Context Type**: Actors Set
- **Source**: Environment
- **Actor Classes**: 
  - StaticMeshActor
  - Landscape
  - Building
- **Tags Required**: "Cover", "Obstacle"

## Scoring Weights Summary

| Test | Weight | Priority |
|------|--------|----------|
| Cover Quality | 0.4 | High |
| Pathfinding | 0.25 | High |
| Distance to Enemy | 0.3 | Medium |
| Tactical Advantage | 0.2 | Medium |
| Line of Sight | 0.2 | Medium |
| Distance to Allies | 0.15 | Low |

## Performance Optimization

### Query Limits
- **Max Results**: 10
- **Max Test Time**: 0.1 seconds
- **Update Frequency**: Every 2 seconds
- **Cache Duration**: 5 seconds

### LOD System
- **High Detail**: Within 500 units (Full tests)
- **Medium Detail**: 500-1000 units (Reduced tests)
- **Low Detail**: 1000+ units (Basic tests only)

## Blueprint Implementation

```cpp
// Example usage in Blueprint
UEnvQuery* CoverQuery = LoadObject<UEnvQuery>(nullptr, TEXT("/Game/AI/EQS/EQS_FindCover"));

FEnvQueryRequest QueryRequest(CoverQuery, GetPawn());
QueryRequest.SetFloatParam("MaxDistance", 1500.0f);
QueryRequest.SetFloatParam("MinDistance", 200.0f);
QueryRequest.SetActorParam("EnemyTarget", CurrentTarget);

FQueryFinishedSignature QueryFinishedDelegate;
QueryFinishedDelegate.BindDynamic(this, &ASquadMateAIController::OnCoverQueryComplete);

QueryRequest.Execute(EEnvQueryRunMode::SingleResult, GetWorld(), QueryFinishedDelegate);
```

## Debug Visualization

### Debug Draw Settings
- **Show Query Results**: True
- **Draw Radius**: 50 units
- **Success Color**: Green
- **Failure Color**: Red
- **Test Color**: Yellow
- **Draw Duration**: 5 seconds

### Console Commands
- `ai.DebugEQS SquadMateAI` - Show EQS debug for specific agent
- `ai.EQSTestPawn` - Run EQS test on selected pawn
- `showdebug EQS` - Toggle EQS debug display

## Common Issues and Solutions

### Issue: No valid cover found
**Solution**: 
- Reduce minimum distance requirements
- Increase search radius
- Check navigation mesh coverage
- Verify cover objects have proper collision

### Issue: AI chooses poor cover positions
**Solution**:
- Adjust scoring weights
- Improve cover quality test
- Add more context information
- Fine-tune distance parameters

### Issue: Performance problems
**Solution**:
- Reduce grid density
- Implement query caching
- Use LOD system
- Limit update frequency

## Testing Scenarios

### Scenario 1: Open Field Combat
- Large search radius
- Emphasis on distance scoring
- Reduced cover quality requirements

### Scenario 2: Urban Environment
- Smaller search radius
- High cover quality requirements
- Strong pathfinding emphasis

### Scenario 3: Squad Coordination
- Increased ally distance weight
- Formation-aware scoring
- Leader proximity bonus
