# SquadMateAI Deployment Guide

This comprehensive guide covers the complete setup and deployment of the SquadMateAI system in Unreal Engine 5.

## Prerequisites

### Engine Requirements
- **Unreal Engine 5.1+**
- **Visual Studio 2022** (for C++ compilation)
- **AI and Navigation modules** enabled
- **Behavior Tree Editor** plugin enabled
- **Environmental Query System** plugin enabled

### Hardware Requirements
- **CPU**: Intel i7-8700K / AMD Ryzen 7 2700X or better
- **RAM**: 16GB minimum, 32GB recommended
- **GPU**: GTX 1070 / RX 580 or better
- **Storage**: 10GB free space for project files

## Installation Steps

### 1. Project Setup

#### Create New Project
```bash
# Create new C++ Third Person project
# Enable AI and Navigation modules
# Set target platform (Windows/Console)
```

#### Module Configuration
1. Add SquadMateAI module to project
2. Update `[ProjectName].uproject`:
```json
{
    "Modules": [
        {
            "Name": "YourProject",
            "Type": "Runtime",
            "LoadingPhase": "Default"
        },
        {
            "Name": "SquadMateAI",
            "Type": "Runtime",
            "LoadingPhase": "Default"
        }
    ]
}
```

#### Build Configuration
1. Copy `SquadMateAI.Build.cs` to `Source/SquadMateAI/`
2. Update project's main Build.cs:
```cpp
PublicDependencyModuleNames.AddRange(new string[] 
{ 
    "Core", "CoreUObject", "Engine", "SquadMateAI"
});
```

### 2. Asset Setup

#### Copy Source Files
```
Source/SquadMateAI/
├── Public/
│   ├── AI/
│   ├── Components/
│   ├── BehaviorTree/
│   └── UI/
└── Private/
    └── [Implementation files]
```

#### Content Assets
```
Content/
├── AI/
│   ├── Blackboards/
│   │   └── BB_SquadMate.uasset
│   ├── BehaviorTrees/
│   │   └── BT_SquadMate.uasset
│   └── EQS/
│       ├── EQS_FindCover.uasset
│       ├── EQS_FlankRoute.uasset
│       └── EQS_ZoneScoring.uasset
├── Characters/
│   └── Victor/
│       ├── BP_SquadMateCharacter.uasset
│       └── ABP_Victor.uasset
└── UI/
    ├── WBP_SquadHUD.uasset
    └── WBP_MatchStats.uasset
```

### 3. Blackboard Setup

#### Create BB_SquadMate
1. **Right-click** in Content Browser → **AI** → **Blackboard**
2. **Name**: `BB_SquadMate`
3. **Add Keys** (refer to BB_SquadMate_Config.md):

| Key Name | Type | Default Value |
|----------|------|---------------|
| TargetActor | Object (Actor) | None |
| TacticState | Enum (ETacticState) | Patrol |
| CoverLocation | Vector | (0,0,0) |
| FlankLocation | Vector | (0,0,0) |
| SquadRole | Enum (ESquadRole) | Assault |
| AllyToRevive | Object (Actor) | None |
| LastKnownEnemyLocation | Vector | (0,0,0) |
| IsInCombat | Bool | False |
| HealthPercentage | Float | 1.0 |
| AmmoCount | Int | 30 |

### 4. Behavior Tree Setup

#### Create BT_SquadMate
1. **Right-click** in Content Browser → **AI** → **Behavior Tree**
2. **Name**: `BT_SquadMate`
3. **Set Blackboard**: `BB_SquadMate`

#### Tree Structure
```
Root
├── Selector (Main Decision)
│   ├── Sequence (Emergency Response)
│   │   ├── Decorator: Check Health < 25%
│   │   └── Task: BTTask_Retreat
│   ├── Sequence (Revival Support)
│   │   ├── Decorator: Check AllyToRevive != None
│   │   └── Task: BTTask_ReviveAlly
│   ├── Sequence (Combat Engagement)
│   │   ├── Decorator: Check TargetActor != None
│   │   ├── Selector (Combat Options)
│   │   │   ├── Sequence (Flanking)
│   │   │   │   ├── Decorator: Check Should Flank
│   │   │   │   └── Task: BTTask_Flank
│   │   │   ├── Sequence (Direct Engagement)
│   │   │   │   ├── Task: BTTask_FindCover
│   │   │   │   └── Task: BTTask_EngageEnemy
│   │   │   └── Task: BTTask_HoldPosition
│   │   └── Service: BTService_TargetScan
│   └── Sequence (Patrol)
│       ├── Task: Move To (Patrol Route)
│       └── Service: BTService_DangerAwareness
```

### 5. EQS Setup

#### Create EQS_FindCover
1. **Right-click** in Content Browser → **AI** → **Environmental Query**
2. **Name**: `EQS_FindCover`
3. **Configure** (refer to EQS_FindCover_Config.md):
   - **Generator**: Grid (20x20, 100 unit spacing)
   - **Tests**: Distance, Line of Sight, Cover Quality, Pathfinding

#### Create EQS_FlankRoute
1. **Name**: `EQS_FlankRoute`
2. **Generator**: Points on Circle (radius 800, 16 points)
3. **Tests**: Flanking angle, enemy visibility, path length

### 6. Character Blueprint Setup

#### Create BP_SquadMateCharacter
1. **Parent Class**: `ASquadMateCharacter`
2. **Add Components**:
   - Health Component
   - Inventory Component
   - Revive Component
   - Squad Role Component
   - Decision Logger Component

#### Configure AI Controller
1. **AI Controller Class**: `ASquadMateAIController`
2. **Behavior Tree**: `BT_SquadMate`
3. **Blackboard**: `BB_SquadMate`

### 7. Animation Blueprint Setup

#### Create ABP_Victor
1. **Parent Class**: `UAnimInstance`
2. **Target Skeleton**: Victor_Skeleton
3. **Configure** (refer to ABP_Victor_Setup.md):
   - State machines for locomotion
   - Blend spaces for movement
   - Animation layers for combat
   - Montages for actions

### 8. Level Setup

#### Create Test Level
1. **New Level**: Basic or Third Person template
2. **Add Navigation Mesh**: Place NavMeshBoundsVolume
3. **Build Navigation**: Build → Build All

#### Place Squad Spawners
```cpp
// Example spawn points for 5v5 match
Team 1 Spawns (Blue):
- Location: (-2000, -1000, 100)
- Location: (-2000, -500, 100)
- Location: (-2000, 0, 100)
- Location: (-2000, 500, 100)
- Location: (-2000, 1000, 100)

Team 2 Spawns (Red):
- Location: (2000, -1000, 100)
- Location: (2000, -500, 100)
- Location: (2000, 0, 100)
- Location: (2000, 500, 100)
- Location: (2000, 1000, 100)
```

#### Add Cover Objects
1. **Static Mesh Actors** with collision
2. **Tag**: "Cover"
3. **Collision**: Block Visibility channel
4. **Height**: 120-200 units for effective cover

#### Place Squad Manager
1. **Add Actor**: `ASquadManager`
2. **Configure**:
   - Team ID: 0 (Blue) or 1 (Red)
   - Max Squad Size: 5
   - Formation: Line or Wedge

### 9. Game Mode Setup

#### Create BP_SquadMateGameMode
```cpp
// Set default classes
Default Pawn Class: BP_SquadMateCharacter
HUD Class: ASquadHUD
Player Controller Class: APlayerController
Game State Class: AGameStateBase
```

#### Configure Match Settings
```cpp
// Match parameters
Match Duration: 600 seconds (10 minutes)
Team Size: 5 players each
Respawn: Disabled (elimination mode)
Victory Condition: Last team standing
```

### 10. Testing and Validation

#### Basic Functionality Test
1. **Place 2 agents** in level
2. **Set different teams** (TeamID 0 vs 1)
3. **Start PIE** (Play in Editor)
4. **Verify**:
   - Agents detect each other
   - Combat engagement occurs
   - Cover seeking behavior
   - Squad coordination

#### Performance Testing
1. **Place 10 agents** (5v5)
2. **Monitor performance**:
   - Frame rate (target: 60 FPS)
   - Memory usage
   - AI update frequency
3. **Optimize** if needed:
   - Reduce EQS frequency
   - Implement LOD system
   - Cache query results

#### Debug Tools
```cpp
// Console commands for testing
ai.DebugEQS SquadMateAI
showdebug AI
showdebug EQS
showdebug BehaviorTree
stat AI
```

## Deployment Checklist

### Pre-Deployment
- [ ] All C++ code compiles without errors
- [ ] Blackboard keys configured correctly
- [ ] Behavior Tree logic validated
- [ ] EQS queries return valid results
- [ ] Animation Blueprint functional
- [ ] Character components working
- [ ] Squad Manager operational
- [ ] UI elements displaying correctly

### Performance Validation
- [ ] 60 FPS with 10 AI agents
- [ ] Memory usage under 4GB
- [ ] No significant frame drops
- [ ] AI response time < 100ms
- [ ] Network sync (if multiplayer)

### Final Testing
- [ ] 5v5 match completion
- [ ] All tactical behaviors working
- [ ] Logging system functional
- [ ] No critical bugs or crashes
- [ ] User interface responsive

## Troubleshooting

### Common Issues

#### AI Not Moving
**Causes**: Navigation mesh missing, invalid spawn location
**Solution**: Rebuild navigation, check spawn points

#### Behavior Tree Not Running
**Causes**: Blackboard not set, AI Controller not assigned
**Solution**: Verify BT references, check AI Controller class

#### EQS Queries Failing
**Causes**: Invalid test parameters, missing context
**Solution**: Check EQS configuration, validate test settings

#### Animation Issues
**Causes**: Missing animation assets, incorrect bone mapping
**Solution**: Verify animation references, check skeleton compatibility

#### Performance Problems
**Causes**: Too many agents, expensive queries, unoptimized code
**Solution**: Implement LOD, reduce update frequency, profile code

### Support Resources
- **Documentation**: Check individual component guides
- **Logs**: Enable verbose AI logging
- **Profiler**: Use Unreal's built-in profiling tools
- **Community**: Unreal Engine forums and Discord
