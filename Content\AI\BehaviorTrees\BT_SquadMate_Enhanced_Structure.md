# BT_SquadMate Enhanced Structure - 5v5 TDM Optimized

This document outlines the complete Behavior Tree structure optimized for PUBG-style 5v5 Team Deathmatch scenarios.

## 🌳 BEHAVIOR TREE HIERARCHY

```
Root
└── Selector: [High-Level Decision Making]
    │
    ├── Sequence: [🔁 REVIVING STATE]
    │   ├── Decorator: Blackboard (IsReviving == true)
    │   └── Task: BTTask_WaitWhileReviving
    │
    ├── Sequence: [🩹 REVIVE ALLY PRIORITY]
    │   ├── Decorator: Blackboard (ReviveTarget != null)
    │   ├── Decorator: Cooldown (2.0s)
    │   ├── Task: BTTask_MoveToReviveTarget
    │   └── Task: BTTask_ReviveAlly
    │
    ├── Sequence: [🚨 UNDER FIRE EMERGENCY]
    │   ├── Decorator: Blackboard (IsUnderFire == true)
    │   ├── Decorator: Cooldown (0.5s)
    │   ├── Parallel: [Emergency Response]
    │   │   ├── Task: BTTask_FindEmergencyCover (EQS)
    │   │   └── Task: BTTask_SuppressiveFire
    │   ├── Task: MoveTo (CoverLocation)
    │   └── Task: BTTask_PeekAndSuppress
    │
    ├── Sequence: [🔫 DIRECT ENGAGEMENT]
    │   ├── Decorator: Blackboard (TargetActor != null)
    │   ├── Decorator: Blackboard (HasLineOfSight == true)
    │   ├── Decorator: Blackboard (AmmoLow == false)
    │   ├── Service: BTService_UpdateCombatState (0.1s)
    │   ├── Selector: [Combat Stance Selection]
    │   │   ├── Sequence: [Close Range Combat]
    │   │   │   ├── Decorator: Distance (< 300 units)
    │   │   │   ├── Task: BTTask_SetStance (Standing/Crouching)
    │   │   │   └── Task: BTTask_AggressiveFire
    │   │   ├── Sequence: [Medium Range Combat]
    │   │   │   ├── Decorator: Distance (300-800 units)
    │   │   │   ├── Task: BTTask_SetStance (Crouching)
    │   │   │   └── Task: BTTask_BurstFire
    │   │   └── Sequence: [Long Range Combat]
    │   │       ├── Decorator: Distance (> 800 units)
    │   │       ├── Task: BTTask_SetStance (Prone/Crouching)
    │   │       └── Task: BTTask_PrecisionFire
    │   └── Task: BTTask_TrackTarget
    │
    ├── Sequence: [🎯 FLANKING MANEUVER]
    │   ├── Decorator: Blackboard (TargetActor != null)
    │   ├── Decorator: Blackboard (HasLineOfSight == false)
    │   ├── Decorator: Blackboard (TacticState == Flank)
    │   ├── Task: BTTask_EQS_FindFlankPosition
    │   ├── Task: MoveTo (FlankLocation)
    │   ├── Task: BTTask_SetStance (Crouching)
    │   └── Task: BTTask_FlankingFire
    │
    ├── Sequence: [🛡️ TACTICAL POSITIONING]
    │   ├── Decorator: Blackboard (TargetActor != null)
    │   ├── Decorator: Blackboard (HasLineOfSight == false)
    │   ├── Task: BTTask_FindTacticalCover (EQS)
    │   ├── Task: MoveTo (CoverLocation)
    │   └── Selector: [Cover Behavior]
    │       ├── Sequence: [Peek and Shoot]
    │       │   ├── Decorator: Blackboard (HasLineOfSight == true)
    │       │   ├── Task: BTTask_PeekFromCover
    │       │   ├── Task: BTTask_AimAndFire
    │       │   └── Task: BTTask_ReturnToCover
    │       └── Task: BTTask_WaitInCover
    │
    ├── Sequence: [🔄 RELOAD AND REPOSITION]
    │   ├── Decorator: Blackboard (AmmoLow == true)
    │   ├── Task: BTTask_FindSafeCover (EQS)
    │   ├── Task: MoveTo (CoverLocation)
    │   ├── Task: BTTask_Reload
    │   └── Task: BTTask_CallForCover
    │
    ├── Sequence: [👥 SQUAD COORDINATION]
    │   ├── Decorator: Blackboard (SquadLeader != null)
    │   ├── Service: BTService_SquadCommunication (0.5s)
    │   ├── Selector: [Role-Based Behavior]
    │   │   ├── Sequence: [Support Role]
    │   │   │   ├── Decorator: Blackboard (RoleType == "Support")
    │   │   │   └── Task: BTTask_SupportBehavior
    │   │   ├── Sequence: [Assault Role]
    │   │   │   ├── Decorator: Blackboard (RoleType == "Assault")
    │   │   │   └── Task: BTTask_AssaultBehavior
    │   │   ├── Sequence: [Scout Role]
    │   │   │   ├── Decorator: Blackboard (RoleType == "Scout")
    │   │   │   └── Task: BTTask_ScoutBehavior
    │   │   ├── Sequence: [Anchor Role]
    │   │   │   ├── Decorator: Blackboard (RoleType == "Anchor")
    │   │   │   └── Task: BTTask_AnchorBehavior
    │   │   └── Sequence: [Sniper Role]
    │   │       ├── Decorator: Blackboard (RoleType == "Sniper")
    │   │       └── Task: BTTask_SniperBehavior
    │   └── Task: BTTask_MaintainFormation
    │
    └── Sequence: [🚶 PATROL AND HOLD]
        ├── Service: BTService_AreaScan (1.0s)
        ├── Selector: [Movement Decision]
        │   ├── Sequence: [Follow Squad Leader]
        │   │   ├── Decorator: Blackboard (SquadLeader != null)
        │   │   ├── Decorator: Distance to Leader (> 500 units)
        │   │   └── Task: MoveTo (SquadLeader)
        │   ├── Sequence: [Patrol Assigned Zone]
        │   │   ├── Decorator: Blackboard (PreferredZone != null)
        │   │   └── Task: BTTask_PatrolZone
        │   └── Task: BTTask_HoldPosition
        └── Task: BTTask_ScanForThreats
```

## 🎮 SERVICES (Continuous Background Tasks)

### BTService_UpdateCombatState
- **Frequency**: 0.1 seconds
- **Purpose**: Update HasLineOfSight, IsUnderFire, AmmoLow
- **Priority**: Critical during combat

### BTService_SquadCommunication
- **Frequency**: 0.5 seconds
- **Purpose**: Share intel, coordinate tactics, request support
- **Priority**: Medium

### BTService_AreaScan
- **Frequency**: 1.0 seconds
- **Purpose**: Detect new threats, update environmental awareness
- **Priority**: Low

### BTService_TargetPrioritization
- **Frequency**: 0.2 seconds
- **Purpose**: Select best target from multiple enemies
- **Priority**: High

## 🎯 DECORATORS (Condition Checks)

### Distance-Based Decorators
- **Close Range**: < 300 units
- **Medium Range**: 300-800 units
- **Long Range**: > 800 units
- **Squad Range**: < 500 units from leader

### State-Based Decorators
- **Health Check**: Health > 25%
- **Ammo Check**: Ammo > 5 rounds
- **Cover Check**: In cover position
- **Formation Check**: Within formation tolerance

### Cooldown Decorators
- **Combat Cooldown**: 0.5 seconds
- **Communication Cooldown**: 2.0 seconds
- **Revive Cooldown**: 3.0 seconds
- **Flank Cooldown**: 5.0 seconds

## 🔧 TASK EXECUTION PRIORITIES

### Priority 1 (Critical - Immediate Response)
1. **BTTask_WaitWhileReviving** - Don't interrupt revive
2. **BTTask_FindEmergencyCover** - Survival under fire
3. **BTTask_SuppressiveFire** - Counter-attack when threatened

### Priority 2 (High - Combat Actions)
1. **BTTask_ReviveAlly** - Team support
2. **BTTask_AggressiveFire** - Direct engagement
3. **BTTask_FlankingFire** - Tactical advantage
4. **BTTask_PeekAndSuppress** - Cover-based combat

### Priority 3 (Medium - Tactical Positioning)
1. **BTTask_FindTacticalCover** - Strategic positioning
2. **BTTask_SetStance** - Optimize for situation
3. **BTTask_Reload** - Maintain combat readiness
4. **BTTask_MaintainFormation** - Squad coordination

### Priority 4 (Low - Background Activities)
1. **BTTask_PatrolZone** - Area control
2. **BTTask_ScanForThreats** - Awareness
3. **BTTask_HoldPosition** - Default state

## 🎲 DYNAMIC BEHAVIOR WEIGHTS

### Combat Intensity Modifiers
- **High Intensity** (3+ enemies): Favor defensive, cover-seeking
- **Medium Intensity** (1-2 enemies): Balanced aggression
- **Low Intensity** (0 enemies): Patrol and positioning

### Role-Based Weight Adjustments
- **Support**: +50% revive priority, +25% cover seeking
- **Assault**: +50% aggressive fire, -25% cover time
- **Scout**: +75% flanking, +50% movement speed
- **Anchor**: +100% hold position, -50% movement
- **Sniper**: +200% long-range engagement, +100% cover seeking

### Health-Based Modifiers
- **>75% Health**: Normal behavior
- **50-75% Health**: +25% cover seeking
- **25-50% Health**: +50% defensive behavior
- **<25% Health**: +100% retreat priority

## 📊 PERFORMANCE OPTIMIZATION

### Update Frequencies by Priority
- **Critical Tasks**: 60 FPS (0.016s)
- **High Priority**: 30 FPS (0.033s)
- **Medium Priority**: 20 FPS (0.05s)
- **Low Priority**: 10 FPS (0.1s)

### LOD System Integration
- **High Detail** (0-500m): Full behavior tree
- **Medium Detail** (500-1000m): Reduced services
- **Low Detail** (1000m+): Basic combat only

### Memory Management
- **Cache Duration**: 5 seconds for EQS results
- **Blackboard Cleanup**: Remove stale references every 10 seconds
- **Decision History**: Keep last 50 decisions per agent

## 🔄 STATE TRANSITIONS

### Combat State Machine
```
Patrol → Engage → [Flank/Hold/Retreat] → Patrol
  ↓         ↓              ↓
Revive ← Support ← Reposition
```

### Emergency Overrides
- **Under Fire**: Immediately interrupt current task
- **Ally Down**: High priority for Support role
- **Low Health**: Force defensive behavior
- **No Ammo**: Force reload sequence

This structure provides a robust, tactical AI system that can handle complex 5v5 scenarios with intelligent decision-making and team coordination.
